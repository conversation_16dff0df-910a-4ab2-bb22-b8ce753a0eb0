# Product Management System

A full-stack application for managing Categories, Subcategories, and Products with interconnected relationships.

## Features

### 🏷️ Category Management
- Create, read, update, and delete categories
- Each category can have multiple subcategories
- Image support for visual representation

### 📁 Subcategory Management
- Create subcategories linked to specific categories
- Dropdown selection of parent categories
- Filter subcategories by category
- Cascading delete (deleting category removes subcategories)

### 📦 Product Management
- Create products linked to specific subcategories
- Cascading dropdowns (Category → Subcategory)
- Complete product information (name, price, color, size, description, image)
- Filter products by category and subcategory
- Rich product cards with all details

## Tech Stack

### Backend
- **Node.js** with Express.js
- **MongoDB** with Mongoose ODM
- **CORS** enabled for frontend communication
- RESTful API design

### Frontend
- **React** with Vite
- **CSS3** with modern styling
- Responsive design
- Interactive forms with validation

## Project Structure

```
sky/
├── backend/
│   ├── src/
│   │   ├── models/
│   │   │   └── model.js          # MongoDB schemas
│   │   ├── routes/
│   │   │   ├── categoryRoute.js  # Category & Subcategory APIs
│   │   │   └── productRoute.js   # Product APIs
│   │   ├── db/
│   │   │   └── db.js            # Database connection
│   │   └── app.js               # Express app configuration
│   ├── index.js                 # Server entry point
│   └── package.json
├── frontend/
│   ├── src/
│   │   ├── components/
│   │   │   ├── CategoryForm.jsx
│   │   │   ├── SubcategoryForm.jsx
│   │   │   ├── ProductForm.jsx
│   │   │   ├── Navigation.jsx
│   │   │   ├── FormStyles.css
│   │   │   └── Navigation.css
│   │   ├── App.jsx
│   │   ├── App.css
│   │   └── main.jsx
│   └── package.json
└── README.md
```

## API Endpoints

### Categories
- `GET /api/categories` - Get all categories
- `GET /api/categories/:id` - Get category by ID
- `POST /api/categories` - Create new category
- `PUT /api/categories/:id` - Update category
- `DELETE /api/categories/:id` - Delete category

### Subcategories
- `GET /api/subcategories` - Get all subcategories
- `GET /api/categories/:categoryId/subcategories` - Get subcategories by category
- `GET /api/subcategories/:id` - Get subcategory by ID
- `POST /api/subcategories` - Create new subcategory
- `PUT /api/subcategories/:id` - Update subcategory
- `DELETE /api/subcategories/:id` - Delete subcategory

### Products
- `GET /api/products` - Get all products
- `GET /api/subcategories/:subcategoryId/products` - Get products by subcategory
- `GET /api/categories/:categoryId/products` - Get products by category
- `GET /api/products/:id` - Get product by ID
- `POST /api/products` - Create new product
- `PUT /api/products/:id` - Update product
- `DELETE /api/products/:id` - Delete product

## Setup Instructions

### Prerequisites
- Node.js (v14 or higher)
- MongoDB (running locally on port 27017)
- npm or yarn

### Backend Setup
1. Navigate to the backend directory:
   ```bash
   cd backend
   ```

2. Install dependencies:
   ```bash
   npm install
   ```

3. Make sure MongoDB is running on your system

4. Start the backend server:
   ```bash
   npm start
   ```
   The server will run on `http://localhost:3000`

### Frontend Setup
1. Navigate to the frontend directory:
   ```bash
   cd frontend
   ```

2. Install dependencies:
   ```bash
   npm install
   ```

3. Start the development server:
   ```bash
   npm run dev
   ```
   The frontend will run on `http://localhost:5173`

## Usage

1. **Start with Categories**: Create categories like "Clothing", "Shoes", etc.
2. **Add Subcategories**: For "Clothing", add subcategories like "Shirts", "Pants"
3. **Create Products**: Add products to specific subcategories with all details

### Example Data Flow
```
Category: Clothing
├── Subcategory: Shirts
│   ├── Product: Cotton T-Shirt (Blue, Medium, $25)
│   └── Product: Formal Shirt (White, Large, $45)
└── Subcategory: Pants
    ├── Product: Jeans (Blue, 32, $60)
    └── Product: Chinos (Khaki, 34, $40)

Category: Shoes
├── Subcategory: Formal
│   └── Product: Oxford Shoes (Black, 10, $120)
└── Subcategory: Casual
    └── Product: Sneakers (White, 9, $80)
```

## Features Highlights

- **Interconnected Forms**: Category selection affects subcategory options
- **Cascading Dropdowns**: Product form shows relevant subcategories based on category
- **Real-time Updates**: Forms update immediately after adding/editing items
- **Responsive Design**: Works on desktop, tablet, and mobile devices
- **Data Validation**: Both frontend and backend validation
- **Error Handling**: User-friendly error messages
- **Filtering**: Filter subcategories and products by parent categories

## Database Schema

### Category
```javascript
{
  name: String (required),
  image: String (required),
  timestamps: true
}
```

### Subcategory
```javascript
{
  name: String (required),
  image: String (required),
  category: ObjectId (ref: 'category', required),
  timestamps: true
}
```

### Product
```javascript
{
  name: String (required),
  image: String (required),
  price: Number (required),
  color: String (required),
  size: String (required),
  description: String (required),
  subCategory: ObjectId (ref: 'subCategory', required),
  timestamps: true
}
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## License

This project is open source and available under the [MIT License](LICENSE).
