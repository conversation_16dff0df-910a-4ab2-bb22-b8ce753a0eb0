const express = require('express');
const cors = require('cors');
const app = express();

// Import simple routes
const simpleRoutes = require('./routes/simpleRoutes');

// Middleware
app.use(cors());
app.use(express.json());

// Routes
app.use('/api', simpleRoutes);

app.get('/', (req, res) => {
    res.json({
        message: 'Simple CRUD API is running!',
        endpoints: {
            categories: '/api/categories',
            subcategories: '/api/subcategories',
            products: '/api/products'
        }
    });
});

module.exports = app;