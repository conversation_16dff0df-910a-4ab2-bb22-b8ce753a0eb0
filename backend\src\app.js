const express = require('express');
const cors = require('cors');
const app = express();

// Import simple routes
const simpleRoutes = require('./routes/simpleRoutes');

// Middleware
app.use(cors());
app.use(express.json());

// Routes
app.use('/api', simpleRoutes);

app.get('/', (req, res) => {
    console.log('Root route hit');
    res.json({
        message: 'Simple CRUD API is running!',
        endpoints: {
            categories: '/api/categories',
            subcategories: '/api/subcategories',
            products: '/api/products'
        }
    });
});

// Debug route
app.get('/test', (req, res) => {
    res.json({ message: 'Test route working' });
});

module.exports = app;