const express = require('express');
const cors = require('cors');
const app = express();

// Import routes
const categoryRoutes=require('./routes/categoryRoute')
const productRoutes = require('./routes/productRoute');

// Middleware
app.use(cors());
app.use(express.json());

// Routes
app.use('/api', categoryRoutes);
app.use('/api', productRoutes);

app.get('/', (req, res) => {
    res.json({ message: 'Category-Subcategory-Product API is running!' });
});

module.exports = app;