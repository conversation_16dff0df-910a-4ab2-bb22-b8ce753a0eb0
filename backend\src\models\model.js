const mongoose = require('mongoose');

// 1. CATEGORY SCHEMA - Parent Level
// Categories like "Clothing", "Shoes", "Electronics"
const categorySchema = mongoose.Schema({
    name: {
        type: String,
        required: true
    },
    
    image: {
        type: String,
        required: true
    }
}, { timestamps: true });

// 2. SUBCATEGORY SCHEMA - Child of Category
// Subcategories like "Shirts", "Pants" under "Clothing"
const subCategorySchema = mongoose.Schema({
    name: {
        type: String,
        required: true
    },
    image: {
        type: String,
        required: true
    },
    category: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'category',
        required: true
    }
}, { timestamps: true });

// 3. PRODUCT SCHEMA - Child of Subcategory
// Products like "Cotton T-Shirt" under "Shirts" subcategory
const productSchema = mongoose.Schema({
    name: {
        type: String,
        required: true
    },
    image: {
        type: String,
        required: true
    },
    price: {
        type: Number,
        required: true
    },
    color: {
        type: String,
        required: true
    },
    size: {
        type: String,
        required: true
    },
    description: {
        type: String,
        required: true
    },
    subCategory: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'subCategory',
        required: true
    }
}, { timestamps: true });

// Create Models
const categoryModel = mongoose.model('category', categorySchema);
const subCategoryModel = mongoose.model('subCategory', subCategorySchema);
const productModel = mongoose.model('product', productSchema);

module.exports = {
    categoryModel,
    subCategoryModel,
    productModel
};





