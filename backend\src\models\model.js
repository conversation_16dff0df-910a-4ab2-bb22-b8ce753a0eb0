const mongoose=require('mongoose');

const categorySchema=mongoose.Schema({
    name:{type:String,required:true},
    image:{type:String,required:true}


},{timestamps:true});

const SubCategorySchema =mongoose.Schema({
    name:
    {
        type:String,
        required:true,
    },
    image:{
        type:String,
        
    required:true},
    category:{
        type:mongoose.Schema.Types.ObjectId,
        ref:'category',required:true}
})

const productSchema =mongoose.Schema({
    name:{type:String,required:true},
    image:{type:String,required:true},
    price:{type:Number,required:true},
    color:{type:String,required:true},
    size:{type:String,required:true},
    description:{type:String,required:true},
    subCategory:{type:mongoose.Schema.Types.ObjectId,ref:'subCategory',required:true}
})

// i have to make a three schemas which are interconnected with each other , the schemas include three things category , subcategory and the product  . i basically got task to makke these category , subcategory and product form with basic react UI but these three sections should be interlinked with each other they should the category schema should contain the the form which drop dowm options of selecting the category for example the category have two sucategory clothing and shoes . Then moving to the sub category  should also have h form with diffent products form listing in the subcategory form , the subcategory for example clothing should have shirt and pent and seperately the other subcategory shoes should have the product listing like formal and shoes 
const categoryModel=mongoose.model('category',categorySchema);
const subCategoryModel=mongoose.model('subCategory',SubCategorySchema);
const productModel=mongoose.model('product',productSchema);







module.exports={
    categoryModel,
    subCategoryModel,
    productModel
};





