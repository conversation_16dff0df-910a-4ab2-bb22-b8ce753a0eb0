const express = require('express');
const router = express.Router();
const { categoryModel, subCategoryModel, productModel } = require('../models/model');

// Category Routes
// Get all categories
router.get('/categories', async (req, res) => {
    try {
        const categories = await categoryModel.find();
        res.status(200).json(categories);
    } catch (error) {
        res.status(500).json({ message: error.message });
    }
});

// Get category by ID
router.get('/categories/:id', async (req, res) => {
    try {
        const category = await categoryModel.findById(req.params.id);
        if (!category) {
            return res.status(404).json({ message: 'Category not found' });
        }
        res.status(200).json(category);
    } catch (error) {
        res.status(500).json({ message: error.message });
    }
});

// Create new category
router.post('/categories', async (req, res) => {
    try {
        const { name, image } = req.body;

        if (!name || !image) {
            return res.status(400).json({ message: 'Name and image are required' });
        }

        const newCategory = new categoryModel({ name, image });
        const savedCategory = await newCategory.save();
        res.status(201).json(savedCategory);
    } catch (error) {
        res.status(500).json({ message: error.message });
    }
});

// Update category
router.put('/categories/:id', async (req, res) => {
    try {
        const { name, image } = req.body;
        const updatedCategory = await categoryModel.findByIdAndUpdate(
            req.params.id,
            { name, image },
            { new: true, runValidators: true }
        );

        if (!updatedCategory) {
            return res.status(404).json({ message: 'Category not found' });
        }

        res.status(200).json(updatedCategory);
    } catch (error) {
        res.status(500).json({ message: error.message });
    }
});

// Delete category
router.delete('/categories/:id', async (req, res) => {
    try {
        const deletedCategory = await categoryModel.findByIdAndDelete(req.params.id);
        

        if (!deletedCategory) {
            return res.status(404).json({ message: 'Category not found' });
        }

        // Also delete related subcategories and products
        await subCategoryModel.deleteMany({ category: req.params.id });
        const subcategories = await subCategoryModel.find({ category: req.params.id });
        const subcategoryIds = subcategories.map(sub => sub._id);
        await productModel.deleteMany({ subCategory: { $in: subcategoryIds } });

        res.status(200).json({ message: 'Category and related data deleted successfully' });
    } catch (error) {
        res.status(500).json({ message: error.message });
    }
});

// Subcategory Routes
// Get all subcategories
router.get('/subcategories', async (req, res) => {
    try {
        const subcategories = await subCategoryModel.find().populate('category', 'name');
        res.status(200).json(subcategories);
    } catch (error) {
        res.status(500).json({ message: error.message });
    }
});

// Get subcategories by category ID
router.get('/categories/:categoryId/subcategories', async (req, res) => {
    try {
        const subcategories = await subCategoryModel.find({ category: req.params.categoryId }).populate('category', 'name');
        res.status(200).json(subcategories);
    } catch (error) {
        res.status(500).json({ message: error.message });
    }
});

// Get subcategory by ID
router.get('/subcategories/:id', async (req, res) => {
    try {
        const subcategory = await subCategoryModel.findById(req.params.id).populate('category', 'name');
        if (!subcategory) {
            return res.status(404).json({ message: 'Subcategory not found' });
        }
        res.status(200).json(subcategory);
    } catch (error) {
        res.status(500).json({ message: error.message });
    }
});

// Create new subcategory
router.post('/subcategories', async (req, res) => {
    try {
        const { name, image, category } = req.body;

        if (!name || !image || !category) {
            return res.status(400).json({ message: 'Name, image, and category are required' });
        }

        // Verify category exists
        const categoryExists = await categoryModel.findById(category);
        if (!categoryExists) {
            return res.status(400).json({ message: 'Invalid category ID' });
        }

        const newSubcategory = new subCategoryModel({ name, image, category });
        const savedSubcategory = await newSubcategory.save();
        const populatedSubcategory = await subCategoryModel.findById(savedSubcategory._id).populate('category', 'name');

        res.status(201).json(populatedSubcategory);
    } catch (error) {
        res.status(500).json({ message: error.message });
    }
});

// Update subcategory
router.put('/subcategories/:id', async (req, res) => {
    try {
        const { name, image, category } = req.body;

        // Verify category exists if provided
        if (category) {
            const categoryExists = await categoryModel.findById(category);
            if (!categoryExists) {
                return res.status(400).json({ message: 'Invalid category ID' });
            }
        }

        const updatedSubcategory = await subCategoryModel.findByIdAndUpdate(
            req.params.id,
            { name, image, category },
            { new: true, runValidators: true }
        ).populate('category', 'name');

        if (!updatedSubcategory) {
            return res.status(404).json({ message: 'Subcategory not found' });
        }

        res.status(200).json(updatedSubcategory);
    } catch (error) {
        res.status(500).json({ message: error.message });
    }
});

// Delete subcategory
router.delete('/subcategories/:id', async (req, res) => {
    try {
        const deletedSubcategory = await subCategoryModel.findByIdAndDelete(req.params.id);

        if (!deletedSubcategory) {
            return res.status(404).json({ message: 'Subcategory not found' });
        }

        // Also delete related products
        await productModel.deleteMany({ subCategory: req.params.id });

        res.status(200).json({ message: 'Subcategory and related products deleted successfully' });
    } catch (error) {
        res.status(500).json({ message: error.message });
    }
});

module.exports = router;