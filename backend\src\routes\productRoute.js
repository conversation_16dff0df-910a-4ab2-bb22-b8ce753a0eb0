const express = require('express');
const router = express.Router();
const { productModel, subCategoryModel } = require('../models/model');

// Product Routes
// Get all products
router.get('/products', async (req, res) => {
    try {
        const products = await productModel.find()
            .populate({
                path: 'subCategory',
                populate: {
                    path: 'category',
                    select: 'name'
                }
            });
        res.status(200).json(products);
    } catch (error) {
        res.status(500).json({ message: error.message });
    }
});

router.get('/products/:id',async(req,res)=>{
    try{
        const product=await productModel.findById(req.params.id).populate({
            path:subcategory,
            populate:{
                path:'category',
                select:'name'
            }
            
        })
        const product = await productModel.findById(req.params.id)
            .populate({
                path: 'subCategory',
                populate: {
                    path: 'category',
                    select: 'name'
                }
            });
        if (!product) {
            return res.status(404).json({ message: 'Product not found' });
        }
        res.status(200).json(product);
    }
    catch(error){
        res.status(500).json({
            message:error.message
        })
    }
})



// Get products by subcategory ID
router.get('/subcategories/:subcategoryId/products', async (req, res) => {
    try {
        const products = await productModel.find({ subCategory: req.params.subcategoryId })
            .populate({
                path: 'subCategory',
                populate: {
                    path: 'category',
                    select: 'name'
                }
            });
        res.status(200).json(products);
    } catch (error) {
        res.status(500).json({ message: error.message });
    }
});

// Get products by category ID (through subcategory)
router.get('/categories/:categoryId/products', async (req, res) => {
    try {
        // First find all subcategories for this category
        const subcategories = await subCategoryModel.find({ category: req.params.categoryId });
        const subcategoryIds = subcategories.map(sub => sub._id);
        
        // Then find all products in those subcategories
        const products = await productModel.find({ subCategory: { $in: subcategoryIds } })
            .populate({
                path: 'subCategory',
                populate: {
                    path: 'category',
                    select: 'name'
                }
            });
        res.status(200).json(products);
    } catch (error) {
        res.status(500).json({ message: error.message });
    }
});

// Get product by ID
router.get('/products/:id', async (req, res) => {
    try {
        const product = await productModel.findById(req.params.id)
            .populate({
                path: 'subCategory',
                populate: {
                    path: 'category',
                    select: 'name'
                }
            });
        if (!product) {
            return res.status(404).json({ message: 'Product not found' });
        }
        res.status(200).json(product);
    } catch (error) {
        res.status(500).json({ message: error.message });
    }
});

// Create new product
router.post('/products', async (req, res) => {
    try {
        const { name, image, price, color, size, description, subCategory } = req.body;
        
        if (!name || !image || !price || !color || !size || !description || !subCategory) {
            return res.status(400).json({ 
                message: 'All fields are required: name, image, price, color, size, description, subCategory' 
            });
        }

        // Verify subcategory exists
        const subcategoryExists = await subCategoryModel.findById(subCategory);
        if (!subcategoryExists) {
            return res.status(400).json({ message: 'Invalid subcategory ID' });
        }

        const newProduct = new productModel({ 
            name, 
            image, 
            price, 
            color, 
            size, 
            description, 
            subCategory 
        });
        const savedProduct = await newProduct.save();
        const populatedProduct = await productModel.findById(savedProduct._id)
            .populate({
                path: 'subCategory',
                populate: {
                    path: 'category',
                    select: 'name'
                }
            });
        
        res.status(201).json(populatedProduct);
    } catch (error) {
        res.status(500).json({ message: error.message });
    }
});

// Update product
router.put('/products/:id', async (req, res) => {
    try {
        const { name, image, price, color, size, description, subCategory } = req.body;
        
        // Verify subcategory exists if provided
        if (subCategory) {
            const subcategoryExists = await subCategoryModel.findById(subCategory);
            if (!subcategoryExists) {
                return res.status(400).json({ message: 'Invalid subcategory ID' });
            }
        }

        const updatedProduct = await productModel.findByIdAndUpdate(
            req.params.id,
            { name, image, price, color, size, description, subCategory },
            { new: true, runValidators: true }
        ).populate({
            path: 'subCategory',
            populate: {
                path: 'category',
                select: 'name'
            }
        });
        
        if (!updatedProduct) {
            return res.status(404).json({ message: 'Product not found' });
        }
        
        res.status(200).json(updatedProduct);
    } catch (error) {
        res.status(500).json({ message: error.message });
    }
});

// Delete product
router.delete('/products/:id', async (req, res) => {
    try {
        const deletedProduct = await productModel.findByIdAndDelete(req.params.id);
        
        if (!deletedProduct) {
            return res.status(404).json({ message: 'Product not found' });
        }
        
        res.status(200).json({ message: 'Product deleted successfully' });
    } catch (error) {
        res.status(500).json({ message: error.message });
    }
});

module.exports = router;
