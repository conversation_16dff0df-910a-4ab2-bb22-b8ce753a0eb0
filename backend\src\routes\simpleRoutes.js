const express = require('express');
const router = express.Router();
const { categoryModel, subCategoryModel, productModel } = require('../models/model');

// ========================================
// CATEGORY CRUD OPERATIONS
// ========================================

// 1. GET ALL CATEGORIES
router.get('/categories', async (req, res) => {
    try {
        const categories = await categoryModel.find();
        res.json(categories);
    } catch (error) {
        res.status(500).json({ message: error.message });
    }
});

// 2. CREATE NEW CATEGORY
router.post('/categories', async (req, res) => {
    try {
        const { name, image } = req.body;
        
        const newCategory = new categoryModel({ name, image });
        const savedCategory = await newCategory.save();
        res.status(201).json(savedCategory);
    } catch (error) {
        res.status(500).json({ message: error.message });
    }
});

// 3. UPDATE CATEGORY
router.put('/categories/:id', async (req, res) => {
    try {
        const { name, image } = req.body;
        const updatedCategory = await categoryModel.findByIdAndUpdate(
            req.params.id,
            { name, image },
            { new: true }
        );
        res.json(updatedCategory);
    } catch (error) {
        res.status(500).json({ message: error.message });
    }
});

// 4. DELETE CATEGORY
router.delete('/categories/:id', async (req, res) => {
    try {
        await categoryModel.findByIdAndDelete(req.params.id);
        res.json({ message: 'Category deleted successfully' });
    } catch (error) {
        res.status(500).json({ message: error.message });
    }
});

// ========================================
// SUBCATEGORY CRUD OPERATIONS
// ========================================

// 1. GET ALL SUBCATEGORIES (with category info)
router.get('/subcategories', async (req, res) => {
    try {
        const subcategories = await subCategoryModel.find().populate('category', 'name');
        res.json(subcategories);
    } catch (error) {
        res.status(500).json({ message: error.message });
    }
});

// 2. GET SUBCATEGORIES BY CATEGORY ID
router.get('/categories/:categoryId/subcategories', async (req, res) => {
    try {
        const subcategories = await subCategoryModel.find({ 
            category: req.params.categoryId 
        }).populate('category', 'name');
        res.json(subcategories);
    } catch (error) {
        res.status(500).json({ message: error.message });
    }
});

// 3. CREATE NEW SUBCATEGORY
router.post('/subcategories', async (req, res) => {
    try {
        const { name, image, category } = req.body;
        
        const newSubcategory = new subCategoryModel({ name, image, category });
        const savedSubcategory = await newSubcategory.save();
        const populatedSubcategory = await subCategoryModel.findById(savedSubcategory._id)
            .populate('category', 'name');
        
        res.status(201).json(populatedSubcategory);
    } catch (error) {
        res.status(500).json({ message: error.message });
    }
});

// 4. UPDATE SUBCATEGORY
router.put('/subcategories/:id', async (req, res) => {
    try {
        const { name, image, category } = req.body;
        const updatedSubcategory = await subCategoryModel.findByIdAndUpdate(
            req.params.id,
            { name, image, category },
            { new: true }
        ).populate('category', 'name');
        
        res.json(updatedSubcategory);
    } catch (error) {
        res.status(500).json({ message: error.message });
    }
});

// 5. DELETE SUBCATEGORY
router.delete('/subcategories/:id', async (req, res) => {
    try {
        await subCategoryModel.findByIdAndDelete(req.params.id);
        res.json({ message: 'Subcategory deleted successfully' });
    } catch (error) {
        res.status(500).json({ message: error.message });
    }
});

// ========================================
// PRODUCT CRUD OPERATIONS
// ========================================

// 1. GET ALL PRODUCTS (with subcategory and category info)
router.get('/products', async (req, res) => {
    try {
        const products = await productModel.find()
            .populate({
                path: 'subCategory',
                populate: {
                    path: 'category',
                    select: 'name'
                }
            });
        res.json(products);
    } catch (error) {
        res.status(500).json({ message: error.message });
    }
});

// 2. GET PRODUCTS BY SUBCATEGORY ID
router.get('/subcategories/:subcategoryId/products', async (req, res) => {
    try {
        const products = await productModel.find({ 
            subCategory: req.params.subcategoryId 
        }).populate({
            path: 'subCategory',
            populate: {
                path: 'category',
                select: 'name'
            }
        });
        res.json(products);
    } catch (error) {
        res.status(500).json({ message: error.message });
    }
});

// 3. CREATE NEW PRODUCT
router.post('/products', async (req, res) => {
    try {
        const { name, image, price, color, size, description, subCategory } = req.body;
        
        const newProduct = new productModel({ 
            name, image, price, color, size, description, subCategory 
        });
        const savedProduct = await newProduct.save();
        const populatedProduct = await productModel.findById(savedProduct._id)
            .populate({
                path: 'subCategory',
                populate: {
                    path: 'category',
                    select: 'name'
                }
            });
        
        res.status(201).json(populatedProduct);
    } catch (error) {
        res.status(500).json({ message: error.message });
    }
});

// 4. UPDATE PRODUCT
router.put('/products/:id', async (req, res) => {
    try {
        const { name, image, price, color, size, description, subCategory } = req.body;
        const updatedProduct = await productModel.findByIdAndUpdate(
            req.params.id,
            { name, image, price, color, size, description, subCategory },
            { new: true }
        ).populate({
            path: 'subCategory',
            populate: {
                path: 'category',
                select: 'name'
            }
        });
        
        res.json(updatedProduct);
    } catch (error) {
        res.status(500).json({ message: error.message });
    }
});

// 5. DELETE PRODUCT
router.delete('/products/:id', async (req, res) => {
    try {
        await productModel.findByIdAndDelete(req.params.id);
        res.json({ message: 'Product deleted successfully' });
    } catch (error) {
        res.status(500).json({ message: error.message });
    }
});

module.exports = router;
