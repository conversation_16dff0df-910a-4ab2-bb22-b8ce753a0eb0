import React, { useState } from 'react';
import SimpleNavigation from './components/SimpleNavigation';
import SimpleCategoryForm from './components/SimpleCategoryForm';
import SimpleSubcategoryForm from './components/SimpleSubcategoryForm';
import SimpleProductForm from './components/SimpleProductForm';

function App() {
  const [activeTab, setActiveTab] = useState('categories');

  const renderActiveComponent = () => {
    switch (activeTab) {
      case 'categories':
        return <SimpleCategoryForm />;
      case 'subcategories':
        return <SimpleSubcategoryForm />;
      case 'products':
        return <SimpleProductForm />;
      default:
        return <SimpleCategoryForm />;
    }
  };

  return (
    <div style={{
      minHeight: '100vh',
      backgroundColor: '#f8f9fa',
      fontFamily: 'Arial, sans-serif'
    }}>
      <SimpleNavigation activeTab={activeTab} setActiveTab={setActiveTab} />
      <main>
        {renderActiveComponent()}
      </main>
    </div>
  );
}

export default App;
