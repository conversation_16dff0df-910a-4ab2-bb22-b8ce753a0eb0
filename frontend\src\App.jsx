import React, { useState } from 'react';
import Navigation from './components/Navigation';
import CategoryForm from './components/CategoryForm';
import SubcategoryForm from './components/SubcategoryForm';
import ProductForm from './components/ProductForm';
import './App.css';

function App() {
  const [activeTab, setActiveTab] = useState('categories');

  const renderActiveComponent = () => {
    switch (activeTab) {
      case 'categories':
        return <CategoryForm onCategoryAdded={() => {}} />;
      case 'subcategories':
        return <SubcategoryForm onSubcategoryAdded={() => {}} />;
      case 'products':
        return <ProductForm onProductAdded={() => {}} />;
      default:
        return <CategoryForm onCategoryAdded={() => {}} />;
    }
  };

  return (
    <div className="app">
      <Navigation activeTab={activeTab} setActiveTab={setActiveTab} />
      <main className="main-content">
        {renderActiveComponent()}
      </main>
    </div>
  );
}

export default App;
