import React, { useState, useEffect } from 'react';
import './FormStyles.css';

const CategoryForm = ({ onCategoryAdded }) => {
    const [categories, setCategories] = useState([]);
    const [formData, setFormData] = useState({
        name: '',
        image: ''
    });
    const [editingId, setEditingId] = useState(null);
    const [loading, setLoading] = useState(false);

    const API_BASE_URL = 'http://localhost:3000/api';

    // Fetch categories on component mount
    useEffect(() => {
        fetchCategories();
    }, []);

    const fetchCategories = async () => {
        try {
            const response = await fetch(`${API_BASE_URL}/categories`);
            const data = await response.json();
            setCategories(data);
        } catch (error) {
            console.error('Error fetching categories:', error);
        }
    };

    const handleInputChange = (e) => {
        const { name, value } = e.target;
        setFormData(prev => ({
            ...prev,
            [name]: value
        }));
    };

    const handleSubmit = async (e) => {
        e.preventDefault();
        setLoading(true);

        try {
            const url = editingId 
                ? `${API_BASE_URL}/categories/${editingId}`
                : `${API_BASE_URL}/categories`;
            
            const method = editingId ? 'PUT' : 'POST';

            const response = await fetch(url, {
                method,
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(formData),
            });

            if (response.ok) {
                const savedCategory = await response.json();
                
                if (editingId) {
                    setCategories(prev => 
                        prev.map(cat => cat._id === editingId ? savedCategory : cat)
                    );
                } else {
                    setCategories(prev => [...prev, savedCategory]);
                }

                // Reset form
                setFormData({ name: '', image: '' });
                setEditingId(null);
                
                // Notify parent component
                if (onCategoryAdded) {
                    onCategoryAdded(savedCategory);
                }
            } else {
                const errorData = await response.json();
                alert(`Error: ${errorData.message}`);
            }
        } catch (error) {
            console.error('Error saving category:', error);
            alert('Error saving category');
        } finally {
            setLoading(false);
        }
    };

    const handleEdit = (category) => {
        setFormData({
            name: category.name,
            image: category.image
        });
        setEditingId(category._id);
    };

    const handleDelete = async (id) => {
        if (window.confirm('Are you sure you want to delete this category? This will also delete all related subcategories and products.')) {
            try {
                const response = await fetch(`${API_BASE_URL}/categories/${id}`, {
                    method: 'DELETE',
                });

                if (response.ok) {
                    setCategories(prev => prev.filter(cat => cat._id !== id));
                } else {
                    const errorData = await response.json();
                    alert(`Error: ${errorData.message}`);
                }
            } catch (error) {
                console.error('Error deleting category:', error);
                alert('Error deleting category');
            }
        }
    };

    const handleCancel = () => {
        setFormData({ name: '', image: '' });
        setEditingId(null);
    };

    return (
        <div className="form-container">
            <h2>Category Management</h2>
            
            <form onSubmit={handleSubmit} className="form">
                <div className="form-group">
                    <label htmlFor="name">Category Name:</label>
                    <input
                        type="text"
                        id="name"
                        name="name"
                        value={formData.name}
                        onChange={handleInputChange}
                        required
                        placeholder="Enter category name"
                    />
                </div>

                <div className="form-group">
                    <label htmlFor="image">Image URL:</label>
                    <input
                        type="url"
                        id="image"
                        name="image"
                        value={formData.image}
                        onChange={handleInputChange}
                        required
                        placeholder="Enter image URL"
                    />
                </div>

                <div className="form-actions">
                    <button type="submit" disabled={loading} className="btn-primary">
                        {loading ? 'Saving...' : editingId ? 'Update Category' : 'Add Category'}
                    </button>
                    {editingId && (
                        <button type="button" onClick={handleCancel} className="btn-secondary">
                            Cancel
                        </button>
                    )}
                </div>
            </form>

            <div className="items-list">
                <h3>Existing Categories</h3>
                {categories.length === 0 ? (
                    <p>No categories found. Add your first category above.</p>
                ) : (
                    <div className="items-grid">
                        {categories.map(category => (
                            <div key={category._id} className="item-card">
                                <img src={category.image} alt={category.name} className="item-image" />
                                <div className="item-content">
                                    <h4>{category.name}</h4>
                                    <div className="item-actions">
                                        <button 
                                            onClick={() => handleEdit(category)}
                                            className="btn-edit"
                                        >
                                            Edit
                                        </button>
                                        <button 
                                            onClick={() => handleDelete(category._id)}
                                            className="btn-delete"
                                        >
                                            Delete
                                        </button>
                                    </div>
                                </div>
                            </div>
                        ))}
                    </div>
                )}
            </div>
        </div>
    );
};

export default CategoryForm;
