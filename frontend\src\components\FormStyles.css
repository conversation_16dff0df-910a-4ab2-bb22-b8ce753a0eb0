/* Modern Form Container */
.form-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
    font-family: 'Inter', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
}

.form-container h2 {
    color: white;
    margin-bottom: 40px;
    text-align: center;
    font-size: 2.5rem;
    font-weight: 700;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
    letter-spacing: -0.5px;
}

/* Modern Form Styles */
.form {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    padding: 40px;
    border-radius: 20px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    margin-bottom: 40px;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 25px;
    margin-bottom: 25px;
}

.form-group {
    margin-bottom: 25px;
}

.form-group label {
    display: block;
    margin-bottom: 10px;
    font-weight: 600;
    color: #2d3748;
    font-size: 1rem;
    letter-spacing: 0.3px;
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 15px 20px;
    border: 2px solid #e2e8f0;
    border-radius: 12px;
    font-size: 1rem;
    transition: all 0.3s ease;
    box-sizing: border-box;
    background: #f8fafc;
    font-family: inherit;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 4px rgba(102, 126, 234, 0.1);
    background: white;
    transform: translateY(-1px);
}

.form-group select:disabled {
    background-color: #f1f5f9;
    color: #94a3b8;
    cursor: not-allowed;
    opacity: 0.7;
}

.form-group textarea {
    resize: vertical;
    min-height: 100px;
    line-height: 1.6;
}

/* Modern Form Actions */
.form-actions {
    display: flex;
    gap: 20px;
    margin-top: 40px;
    justify-content: center;
}

.btn-primary {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    border: none;
    padding: 15px 35px;
    border-radius: 12px;
    font-size: 1.1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
    letter-spacing: 0.5px;
}

.btn-primary:hover:not(:disabled) {
    background: linear-gradient(135deg, #5a67d8, #6b46c1);
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
}

.btn-primary:disabled {
    background: #94a3b8;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

.btn-secondary {
    background: linear-gradient(135deg, #718096, #4a5568);
    color: white;
    border: none;
    padding: 15px 35px;
    border-radius: 12px;
    font-size: 1.1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(113, 128, 150, 0.3);
}

.btn-secondary:hover {
    background: linear-gradient(135deg, #4a5568, #2d3748);
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(113, 128, 150, 0.4);
}

/* Modern Items List */
.items-list {
    margin-top: 50px;
}

.items-list h3 {
    color: white;
    margin-bottom: 30px;
    font-size: 2rem;
    text-align: center;
    font-weight: 600;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.list-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
    flex-wrap: wrap;
    gap: 25px;
    background: rgba(255, 255, 255, 0.1);
    padding: 20px;
    border-radius: 15px;
    backdrop-filter: blur(10px);
}

.filter-controls {
    display: flex;
    gap: 25px;
    flex-wrap: wrap;
}

.filter-group {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.filter-group label {
    font-size: 0.95rem;
    font-weight: 600;
    color: white;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.filter-group select {
    padding: 12px 16px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-radius: 10px;
    font-size: 0.95rem;
    min-width: 180px;
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(10px);
}

/* Centered Grid Layout */
.items-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
    gap: 30px;
    justify-items: center;
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 20px;
}

.item-card {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(15px);
    border-radius: 20px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    transition: all 0.4s ease;
    border: 1px solid rgba(255, 255, 255, 0.2);
    width: 100%;
    max-width: 350px;
}

.item-card:hover {
    transform: translateY(-10px) scale(1.02);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
    border-color: rgba(102, 126, 234, 0.3);
}

.item-image {
    width: 100%;
    height: 220px;
    object-fit: cover;
    transition: transform 0.4s ease;
}

.item-card:hover .item-image {
    transform: scale(1.05);
}

.item-content {
    padding: 25px;
}

.item-content h4 {
    margin: 0 0 12px 0;
    color: #2d3748;
    font-size: 1.3rem;
    font-weight: 700;
    line-height: 1.3;
}

.item-content p {
    margin: 8px 0;
    color: #4a5568;
    font-size: 0.95rem;
    line-height: 1.5;
}

.price {
    font-size: 1.4rem !important;
    font-weight: 800 !important;
    color: #10b981 !important;
    background: linear-gradient(135deg, #10b981, #059669);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.details {
    font-style: italic;
    color: #6b7280 !important;
    font-size: 0.9rem !important;
}

.description {
    margin: 15px 0 !important;
    line-height: 1.6;
    color: #4b5563 !important;
}

/* Modern Badges */
.badges {
    display: flex;
    gap: 10px;
    margin: 20px 0;
    flex-wrap: wrap;
    justify-content: center;
}

.category-badge,
.subcategory-badge {
    padding: 8px 16px;
    border-radius: 25px;
    font-size: 0.85rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    transition: transform 0.2s ease;
}

.category-badge:hover,
.subcategory-badge:hover {
    transform: translateY(-2px);
}

.category-badge {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
}

.subcategory-badge {
    background: linear-gradient(135deg, #f093fb, #f5576c);
    color: white;
}

/* Modern Item Actions */
.item-actions {
    display: flex;
    gap: 12px;
    margin-top: 25px;
    justify-content: center;
}

.btn-edit,
.btn-delete {
    flex: 1;
    padding: 12px 20px;
    border: none;
    border-radius: 12px;
    font-size: 0.95rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    letter-spacing: 0.3px;
}

.btn-edit {
    background: linear-gradient(135deg, #fbbf24, #f59e0b);
    color: white;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

.btn-edit:hover {
    background: linear-gradient(135deg, #f59e0b, #d97706);
    transform: translateY(-3px);
    box-shadow: 0 8px 20px rgba(245, 158, 11, 0.3);
}

.btn-delete {
    background: linear-gradient(135deg, #ef4444, #dc2626);
    color: white;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

.btn-delete:hover {
    background: linear-gradient(135deg, #dc2626, #b91c1c);
    transform: translateY(-3px);
    box-shadow: 0 8px 20px rgba(239, 68, 68, 0.3);
}

/* Product Card Specific */
.product-card .item-content {
    padding-bottom: 15px;
}

/* Responsive Design */
@media (max-width: 768px) {
    .form-container {
        padding: 15px;
    }
    
    .form {
        padding: 20px;
    }
    
    .form-row {
        grid-template-columns: 1fr;
        gap: 15px;
    }
    
    .list-header {
        flex-direction: column;
        align-items: stretch;
    }
    
    .filter-controls {
        flex-direction: column;
    }
    
    .items-grid {
        grid-template-columns: 1fr;
    }
    
    .form-actions {
        flex-direction: column;
    }
    
    .item-actions {
        flex-direction: column;
    }
}

@media (max-width: 480px) {
    .form-container h2 {
        font-size: 1.5rem;
    }
    
    .items-list h3 {
        font-size: 1.2rem;
    }
    
    .filter-group select {
        min-width: 120px;
    }
}
