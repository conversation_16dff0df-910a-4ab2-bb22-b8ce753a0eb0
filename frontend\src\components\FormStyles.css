/* Form Container */
.form-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.form-container h2 {
    color: #333;
    margin-bottom: 30px;
    text-align: center;
    font-size: 2rem;
    border-bottom: 3px solid #007bff;
    padding-bottom: 10px;
}

/* Form Styles */
.form {
    background: #f8f9fa;
    padding: 30px;
    border-radius: 10px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    margin-bottom: 40px;
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
    margin-bottom: 20px;
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
    color: #555;
    font-size: 0.95rem;
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 12px 15px;
    border: 2px solid #e1e5e9;
    border-radius: 6px;
    font-size: 1rem;
    transition: border-color 0.3s ease, box-shadow 0.3s ease;
    box-sizing: border-box;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: #007bff;
    box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.1);
}

.form-group select:disabled {
    background-color: #f8f9fa;
    color: #6c757d;
    cursor: not-allowed;
}

.form-group textarea {
    resize: vertical;
    min-height: 80px;
}

/* Form Actions */
.form-actions {
    display: flex;
    gap: 15px;
    margin-top: 30px;
}

.btn-primary {
    background: linear-gradient(135deg, #007bff, #0056b3);
    color: white;
    border: none;
    padding: 12px 30px;
    border-radius: 6px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
}

.btn-primary:hover:not(:disabled) {
    background: linear-gradient(135deg, #0056b3, #004085);
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 123, 255, 0.3);
}

.btn-primary:disabled {
    background: #6c757d;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

.btn-secondary {
    background: #6c757d;
    color: white;
    border: none;
    padding: 12px 30px;
    border-radius: 6px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
}

.btn-secondary:hover {
    background: #545b62;
    transform: translateY(-2px);
}

/* Items List */
.items-list {
    margin-top: 40px;
}

.items-list h3 {
    color: #333;
    margin-bottom: 25px;
    font-size: 1.5rem;
}

.list-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 25px;
    flex-wrap: wrap;
    gap: 20px;
}

.filter-controls {
    display: flex;
    gap: 20px;
    flex-wrap: wrap;
}

.filter-group {
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.filter-group label {
    font-size: 0.9rem;
    font-weight: 600;
    color: #555;
}

.filter-group select {
    padding: 8px 12px;
    border: 2px solid #e1e5e9;
    border-radius: 4px;
    font-size: 0.9rem;
    min-width: 150px;
}

/* Items Grid */
.items-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 25px;
}

.item-card {
    background: white;
    border-radius: 10px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.item-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 15px rgba(0, 0, 0, 0.15);
}

.item-image {
    width: 100%;
    height: 200px;
    object-fit: cover;
    border-bottom: 1px solid #e1e5e9;
}

.item-content {
    padding: 20px;
}

.item-content h4 {
    margin: 0 0 10px 0;
    color: #333;
    font-size: 1.2rem;
    font-weight: 600;
}

.item-content p {
    margin: 5px 0;
    color: #666;
    font-size: 0.9rem;
}

.price {
    font-size: 1.1rem !important;
    font-weight: 700 !important;
    color: #28a745 !important;
}

.details {
    font-style: italic;
}

.description {
    margin: 10px 0 !important;
    line-height: 1.4;
}

/* Badges */
.badges {
    display: flex;
    gap: 8px;
    margin: 15px 0;
    flex-wrap: wrap;
}

.category-badge,
.subcategory-badge {
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.category-badge {
    background: linear-gradient(135deg, #007bff, #0056b3);
    color: white;
}

.subcategory-badge {
    background: linear-gradient(135deg, #28a745, #1e7e34);
    color: white;
}

/* Item Actions */
.item-actions {
    display: flex;
    gap: 10px;
    margin-top: 15px;
}

.btn-edit,
.btn-delete {
    flex: 1;
    padding: 8px 16px;
    border: none;
    border-radius: 4px;
    font-size: 0.9rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
}

.btn-edit {
    background: #ffc107;
    color: #212529;
}

.btn-edit:hover {
    background: #e0a800;
    transform: translateY(-1px);
}

.btn-delete {
    background: #dc3545;
    color: white;
}

.btn-delete:hover {
    background: #c82333;
    transform: translateY(-1px);
}

/* Product Card Specific */
.product-card .item-content {
    padding-bottom: 15px;
}

/* Responsive Design */
@media (max-width: 768px) {
    .form-container {
        padding: 15px;
    }
    
    .form {
        padding: 20px;
    }
    
    .form-row {
        grid-template-columns: 1fr;
        gap: 15px;
    }
    
    .list-header {
        flex-direction: column;
        align-items: stretch;
    }
    
    .filter-controls {
        flex-direction: column;
    }
    
    .items-grid {
        grid-template-columns: 1fr;
    }
    
    .form-actions {
        flex-direction: column;
    }
    
    .item-actions {
        flex-direction: column;
    }
}

@media (max-width: 480px) {
    .form-container h2 {
        font-size: 1.5rem;
    }
    
    .items-list h3 {
        font-size: 1.2rem;
    }
    
    .filter-group select {
        min-width: 120px;
    }
}
