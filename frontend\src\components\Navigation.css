.navigation {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    position: sticky;
    top: 0;
    z-index: 1000;
}

.nav-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    min-height: 70px;
}

.nav-brand h1 {
    color: white;
    margin: 0;
    font-size: 1.5rem;
    font-weight: 700;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.nav-tabs {
    display: flex;
    gap: 5px;
}

.nav-tab {
    background: rgba(255, 255, 255, 0.1);
    border: 2px solid rgba(255, 255, 255, 0.2);
    color: white;
    padding: 12px 20px;
    border-radius: 25px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 8px;
    font-weight: 600;
    font-size: 0.95rem;
    backdrop-filter: blur(10px);
}

.nav-tab:hover {
    background: rgba(255, 255, 255, 0.2);
    border-color: rgba(255, 255, 255, 0.4);
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.nav-tab.active {
    background: white;
    color: #667eea;
    border-color: white;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.nav-tab.active:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.3);
}

.tab-icon {
    font-size: 1.2rem;
}

.tab-label {
    font-weight: 600;
}

/* Responsive Design */
@media (max-width: 768px) {
    .nav-container {
        flex-direction: column;
        padding: 15px 20px;
        gap: 15px;
        min-height: auto;
    }
    
    .nav-brand h1 {
        font-size: 1.3rem;
        text-align: center;
    }
    
    .nav-tabs {
        width: 100%;
        justify-content: center;
        flex-wrap: wrap;
        gap: 10px;
    }
    
    .nav-tab {
        flex: 1;
        min-width: 120px;
        justify-content: center;
        padding: 10px 15px;
        font-size: 0.9rem;
    }
}

@media (max-width: 480px) {
    .nav-container {
        padding: 10px 15px;
    }
    
    .nav-brand h1 {
        font-size: 1.1rem;
    }
    
    .nav-tabs {
        flex-direction: column;
        width: 100%;
    }
    
    .nav-tab {
        width: 100%;
        justify-content: center;
    }
    
    .tab-icon {
        font-size: 1rem;
    }
    
    .tab-label {
        font-size: 0.85rem;
    }
}
