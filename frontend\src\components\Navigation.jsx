import React from 'react';
import './Navigation.css';

const Navigation = ({ activeTab, setActiveTab }) => {
    const tabs = [
        { id: 'categories', label: 'Categories', icon: '📂' },
        { id: 'subcategories', label: 'Subcategories', icon: '📁' },
        { id: 'products', label: 'Products', icon: '📦' }
    ];

    return (
        <nav className="navigation">
            <div className="nav-container">
                <div className="nav-brand">
                    <h1>🛍️ Product Management System</h1>
                </div>
                <div className="nav-tabs">
                    {tabs.map(tab => (
                        <button
                            key={tab.id}
                            className={`nav-tab ${activeTab === tab.id ? 'active' : ''}`}
                            onClick={() => setActiveTab(tab.id)}
                        >
                            <span className="tab-icon">{tab.icon}</span>
                            <span className="tab-label">{tab.label}</span>
                        </button>
                    ))}
                </div>
            </div>
        </nav>
    );
};

export default Navigation;
