import React, { useState, useEffect } from 'react';
import './FormStyles.css';

const ProductForm = ({ onProductAdded }) => {
    const [categories, setCategories] = useState([]);
    const [subcategories, setSubcategories] = useState([]);
    const [products, setProducts] = useState([]);
    const [formData, setFormData] = useState({
        name: '',
        image: '',
        price: '',
        color: '',
        size: '',
        description: '',
        category: '',
        subCategory: ''
    });
    const [editingId, setEditingId] = useState(null);
    const [loading, setLoading] = useState(false);
    const [selectedCategoryFilter, setSelectedCategoryFilter] = useState('');
    const [selectedSubcategoryFilter, setSelectedSubcategoryFilter] = useState('');

    const API_BASE_URL = 'http://localhost:3000/api';

    // Fetch data on component mount
    useEffect(() => {
        fetchCategories();
        fetchProducts();
    }, []);

    // Fetch subcategories when category is selected
    useEffect(() => {
        if (formData.category) {
            fetchSubcategoriesByCategory(formData.category);
        } else {
            setSubcategories([]);
            setFormData(prev => ({ ...prev, subCategory: '' }));
        }
    }, [formData.category]);

    const fetchCategories = async () => {
        try {
            const response = await fetch(`${API_BASE_URL}/categories`);
            const data = await response.json();
            setCategories(data);
        } catch (error) {
            console.error('Error fetching categories:', error);
        }
    };

    const fetchSubcategoriesByCategory = async (categoryId) => {
        try {
            const response = await fetch(`${API_BASE_URL}/categories/${categoryId}/subcategories`);
            const data = await response.json();
            setSubcategories(data);
        } catch (error) {
            console.error('Error fetching subcategories:', error);
        }
    };

    const fetchProducts = async () => {
        try {
            const response = await fetch(`${API_BASE_URL}/products`);
            const data = await response.json();
            setProducts(data);
        } catch (error) {
            console.error('Error fetching products:', error);
        }
    };

    const handleInputChange = (e) => {
        const { name, value } = e.target;
        setFormData(prev => ({
            ...prev,
            [name]: value
        }));
    };

    const handleSubmit = async (e) => {
        e.preventDefault();
        setLoading(true);

        try {
            // Remove category from form data as it's not needed for the API
            const { category, ...productData } = formData;
            
            const url = editingId 
                ? `${API_BASE_URL}/products/${editingId}`
                : `${API_BASE_URL}/products`;
            
            const method = editingId ? 'PUT' : 'POST';

            const response = await fetch(url, {
                method,
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(productData),
            });

            if (response.ok) {
                const savedProduct = await response.json();
                
                if (editingId) {
                    setProducts(prev => 
                        prev.map(prod => prod._id === editingId ? savedProduct : prod)
                    );
                } else {
                    setProducts(prev => [...prev, savedProduct]);
                }

                // Reset form
                setFormData({ 
                    name: '', 
                    image: '', 
                    price: '', 
                    color: '', 
                    size: '', 
                    description: '', 
                    category: '', 
                    subCategory: '' 
                });
                setEditingId(null);
                
                // Notify parent component
                if (onProductAdded) {
                    onProductAdded(savedProduct);
                }
            } else {
                const errorData = await response.json();
                alert(`Error: ${errorData.message}`);
            }
        } catch (error) {
            console.error('Error saving product:', error);
            alert('Error saving product');
        } finally {
            setLoading(false);
        }
    };

    const handleEdit = async (product) => {
        // First fetch subcategories for the product's category
        await fetchSubcategoriesByCategory(product.subCategory.category._id);
        
        setFormData({
            name: product.name,
            image: product.image,
            price: product.price.toString(),
            color: product.color,
            size: product.size,
            description: product.description,
            category: product.subCategory.category._id,
            subCategory: product.subCategory._id
        });
        setEditingId(product._id);
    };

    const handleDelete = async (id) => {
        if (window.confirm('Are you sure you want to delete this product?')) {
            try {
                const response = await fetch(`${API_BASE_URL}/products/${id}`, {
                    method: 'DELETE',
                });

                if (response.ok) {
                    setProducts(prev => prev.filter(prod => prod._id !== id));
                } else {
                    const errorData = await response.json();
                    alert(`Error: ${errorData.message}`);
                }
            } catch (error) {
                console.error('Error deleting product:', error);
                alert('Error deleting product');
            }
        }
    };

    const handleCancel = () => {
        setFormData({ 
            name: '', 
            image: '', 
            price: '', 
            color: '', 
            size: '', 
            description: '', 
            category: '', 
            subCategory: '' 
        });
        setEditingId(null);
    };

    // Filter products based on selected filters
    const filteredProducts = products.filter(product => {
        if (selectedCategoryFilter && product.subCategory.category._id !== selectedCategoryFilter) {
            return false;
        }
        if (selectedSubcategoryFilter && product.subCategory._id !== selectedSubcategoryFilter) {
            return false;
        }
        return true;
    });

    return (
        <div className="form-container">
            <h2>Product Management</h2>
            
            <form onSubmit={handleSubmit} className="form">
                <div className="form-row">
                    <div className="form-group">
                        <label htmlFor="category">Select Category:</label>
                        <select
                            id="category"
                            name="category"
                            value={formData.category}
                            onChange={handleInputChange}
                            required
                        >
                            <option value="">Choose a category</option>
                            {categories.map(category => (
                                <option key={category._id} value={category._id}>
                                    {category.name}
                                </option>
                            ))}
                        </select>
                    </div>

                    <div className="form-group">
                        <label htmlFor="subCategory">Select Subcategory:</label>
                        <select
                            id="subCategory"
                            name="subCategory"
                            value={formData.subCategory}
                            onChange={handleInputChange}
                            required
                            disabled={!formData.category}
                        >
                            <option value="">Choose a subcategory</option>
                            {subcategories.map(subcategory => (
                                <option key={subcategory._id} value={subcategory._id}>
                                    {subcategory.name}
                                </option>
                            ))}
                        </select>
                    </div>
                </div>

                <div className="form-row">
                    <div className="form-group">
                        <label htmlFor="name">Product Name:</label>
                        <input
                            type="text"
                            id="name"
                            name="name"
                            value={formData.name}
                            onChange={handleInputChange}
                            required
                            placeholder="Enter product name"
                        />
                    </div>

                    <div className="form-group">
                        <label htmlFor="price">Price:</label>
                        <input
                            type="number"
                            id="price"
                            name="price"
                            value={formData.price}
                            onChange={handleInputChange}
                            required
                            min="0"
                            step="0.01"
                            placeholder="Enter price"
                        />
                    </div>
                </div>

                <div className="form-row">
                    <div className="form-group">
                        <label htmlFor="color">Color:</label>
                        <input
                            type="text"
                            id="color"
                            name="color"
                            value={formData.color}
                            onChange={handleInputChange}
                            required
                            placeholder="Enter color"
                        />
                    </div>

                    <div className="form-group">
                        <label htmlFor="size">Size:</label>
                        <input
                            type="text"
                            id="size"
                            name="size"
                            value={formData.size}
                            onChange={handleInputChange}
                            required
                            placeholder="Enter size"
                        />
                    </div>
                </div>

                <div className="form-group">
                    <label htmlFor="image">Image URL:</label>
                    <input
                        type="url"
                        id="image"
                        name="image"
                        value={formData.image}
                        onChange={handleInputChange}
                        required
                        placeholder="Enter image URL"
                    />
                </div>

                <div className="form-group">
                    <label htmlFor="description">Description:</label>
                    <textarea
                        id="description"
                        name="description"
                        value={formData.description}
                        onChange={handleInputChange}
                        required
                        placeholder="Enter product description"
                        rows="3"
                    />
                </div>

                <div className="form-actions">
                    <button type="submit" disabled={loading} className="btn-primary">
                        {loading ? 'Saving...' : editingId ? 'Update Product' : 'Add Product'}
                    </button>
                    {editingId && (
                        <button type="button" onClick={handleCancel} className="btn-secondary">
                            Cancel
                        </button>
                    )}
                </div>
            </form>

            <div className="items-list">
                <div className="list-header">
                    <h3>Existing Products</h3>
                    <div className="filter-controls">
                        <div className="filter-group">
                            <label htmlFor="categoryFilter">Filter by Category:</label>
                            <select
                                id="categoryFilter"
                                value={selectedCategoryFilter}
                                onChange={(e) => {
                                    setSelectedCategoryFilter(e.target.value);
                                    setSelectedSubcategoryFilter(''); // Reset subcategory filter
                                }}
                            >
                                <option value="">All Categories</option>
                                {categories.map(category => (
                                    <option key={category._id} value={category._id}>
                                        {category.name}
                                    </option>
                                ))}
                            </select>
                        </div>
                        
                        <div className="filter-group">
                            <label htmlFor="subcategoryFilter">Filter by Subcategory:</label>
                            <select
                                id="subcategoryFilter"
                                value={selectedSubcategoryFilter}
                                onChange={(e) => setSelectedSubcategoryFilter(e.target.value)}
                                disabled={!selectedCategoryFilter}
                            >
                                <option value="">All Subcategories</option>
                                {products
                                    .filter(p => !selectedCategoryFilter || p.subCategory.category._id === selectedCategoryFilter)
                                    .map(p => p.subCategory)
                                    .filter((sub, index, self) => self.findIndex(s => s._id === sub._id) === index)
                                    .map(subcategory => (
                                        <option key={subcategory._id} value={subcategory._id}>
                                            {subcategory.name}
                                        </option>
                                    ))
                                }
                            </select>
                        </div>
                    </div>
                </div>

                {filteredProducts.length === 0 ? (
                    <p>No products found. Add your first product above.</p>
                ) : (
                    <div className="items-grid">
                        {filteredProducts.map(product => (
                            <div key={product._id} className="item-card product-card">
                                <img src={product.image} alt={product.name} className="item-image" />
                                <div className="item-content">
                                    <h4>{product.name}</h4>
                                    <p className="price">${product.price}</p>
                                    <p className="details">Color: {product.color} | Size: {product.size}</p>
                                    <p className="description">{product.description}</p>
                                    <div className="badges">
                                        <span className="category-badge">{product.subCategory.category.name}</span>
                                        <span className="subcategory-badge">{product.subCategory.name}</span>
                                    </div>
                                    <div className="item-actions">
                                        <button 
                                            onClick={() => handleEdit(product)}
                                            className="btn-edit"
                                        >
                                            Edit
                                        </button>
                                        <button 
                                            onClick={() => handleDelete(product._id)}
                                            className="btn-delete"
                                        >
                                            Delete
                                        </button>
                                    </div>
                                </div>
                            </div>
                        ))}
                    </div>
                )}
            </div>
        </div>
    );
};

export default ProductForm;
