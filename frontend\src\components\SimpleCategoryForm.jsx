import React, { useState, useEffect } from 'react';

const SimpleCategoryForm = () => {
    // State for form data
    const [formData, setFormData] = useState({
        name: '',
        image: ''
    });
    
    // State for categories list
    const [categories, setCategories] = useState([]);
    
    // State for editing
    const [editingId, setEditingId] = useState(null);

    // API base URL
    const API_URL = 'http://localhost:3000/api';

    // Fetch categories when component loads
    useEffect(() => {
        fetchCategories();
    }, []);

    // Function to fetch all categories
    const fetchCategories = async () => {
        try {
            const response = await fetch(`${API_URL}/categories`);
            const data = await response.json();
            setCategories(data);
        } catch (error) {
            console.error('Error fetching categories:', error);
        }
    };

    // Handle input changes
    const handleInputChange = (e) => {
        const { name, value } = e.target;
        setFormData(prev => ({
            ...prev,
            [name]: value
        }));
    };

    // Handle form submission
    const handleSubmit = async (e) => {
        e.preventDefault();
        
        try {
            const url = editingId 
                ? `${API_URL}/categories/${editingId}`
                : `${API_URL}/categories`;
            
            const method = editingId ? 'PUT' : 'POST';

            const response = await fetch(url, {
                method: method,
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(formData),
            });

            if (response.ok) {
                // Reset form
                setFormData({ name: '', image: '' });
                setEditingId(null);
                
                // Refresh categories list
                fetchCategories();
                
                alert(editingId ? 'Category updated!' : 'Category created!');
            }
        } catch (error) {
            console.error('Error saving category:', error);
            alert('Error saving category');
        }
    };

    // Handle edit button click
    const handleEdit = (category) => {
        setFormData({
            name: category.name,
            image: category.image
        });
        setEditingId(category._id);
    };

    // Handle delete button click
    const handleDelete = async (id) => {
        if (window.confirm('Are you sure you want to delete this category?')) {
            try {
                const response = await fetch(`${API_URL}/categories/${id}`, {
                    method: 'DELETE',
                });

                if (response.ok) {
                    fetchCategories();
                    alert('Category deleted!');
                }
            } catch (error) {
                console.error('Error deleting category:', error);
                alert('Error deleting category');
            }
        }
    };

    // Handle cancel edit
    const handleCancel = () => {
        setFormData({ name: '', image: '' });
        setEditingId(null);
    };

    return (
        <div style={{ padding: '20px', maxWidth: '800px', margin: '0 auto' }}>
            <h2>Category Management</h2>
            
            {/* Form Section */}
            <div style={{ 
                backgroundColor: '#f5f5f5', 
                padding: '20px', 
                borderRadius: '8px', 
                marginBottom: '30px' 
            }}>
                <h3>{editingId ? 'Edit Category' : 'Add New Category'}</h3>
                
                <form onSubmit={handleSubmit}>
                    <div style={{ marginBottom: '15px' }}>
                        <label style={{ display: 'block', marginBottom: '5px' }}>
                            Category Name:
                        </label>
                        <input
                            type="text"
                            name="name"
                            value={formData.name}
                            onChange={handleInputChange}
                            required
                            style={{
                                width: '100%',
                                padding: '8px',
                                border: '1px solid #ddd',
                                borderRadius: '4px'
                            }}
                            placeholder="Enter category name (e.g., Clothing, Shoes)"
                        />
                    </div>

                    <div style={{ marginBottom: '15px' }}>
                        <label style={{ display: 'block', marginBottom: '5px' }}>
                            Image URL:
                        </label>
                        <input
                            type="url"
                            name="image"
                            value={formData.image}
                            onChange={handleInputChange}
                            required
                            style={{
                                width: '100%',
                                padding: '8px',
                                border: '1px solid #ddd',
                                borderRadius: '4px'
                            }}
                            placeholder="Enter image URL"
                        />
                    </div>

                    <div>
                        <button 
                            type="submit"
                            style={{
                                backgroundColor: '#007bff',
                                color: 'white',
                                padding: '10px 20px',
                                border: 'none',
                                borderRadius: '4px',
                                cursor: 'pointer',
                                marginRight: '10px'
                            }}
                        >
                            {editingId ? 'Update Category' : 'Add Category'}
                        </button>
                        
                        {editingId && (
                            <button 
                                type="button"
                                onClick={handleCancel}
                                style={{
                                    backgroundColor: '#6c757d',
                                    color: 'white',
                                    padding: '10px 20px',
                                    border: 'none',
                                    borderRadius: '4px',
                                    cursor: 'pointer'
                                }}
                            >
                                Cancel
                            </button>
                        )}
                    </div>
                </form>
            </div>

            {/* Categories List Section */}
            <div>
                <h3>Existing Categories</h3>
                {categories.length === 0 ? (
                    <p>No categories found. Add your first category above.</p>
                ) : (
                    <div style={{ 
                        display: 'grid', 
                        gridTemplateColumns: 'repeat(auto-fill, minmax(250px, 1fr))', 
                        gap: '20px' 
                    }}>
                        {categories.map(category => (
                            <div 
                                key={category._id} 
                                style={{
                                    border: '1px solid #ddd',
                                    borderRadius: '8px',
                                    padding: '15px',
                                    backgroundColor: 'white'
                                }}
                            >
                                <img 
                                    src={category.image} 
                                    alt={category.name}
                                    style={{
                                        width: '100%',
                                        height: '150px',
                                        objectFit: 'cover',
                                        borderRadius: '4px',
                                        marginBottom: '10px'
                                    }}
                                />
                                <h4 style={{ margin: '0 0 10px 0' }}>{category.name}</h4>
                                <div>
                                    <button 
                                        onClick={() => handleEdit(category)}
                                        style={{
                                            backgroundColor: '#ffc107',
                                            color: 'black',
                                            padding: '5px 10px',
                                            border: 'none',
                                            borderRadius: '4px',
                                            cursor: 'pointer',
                                            marginRight: '5px'
                                        }}
                                    >
                                        Edit
                                    </button>
                                    <button 
                                        onClick={() => handleDelete(category._id)}
                                        style={{
                                            backgroundColor: '#dc3545',
                                            color: 'white',
                                            padding: '5px 10px',
                                            border: 'none',
                                            borderRadius: '4px',
                                            cursor: 'pointer'
                                        }}
                                    >
                                        Delete
                                    </button>
                                </div>
                            </div>
                        ))}
                    </div>
                )}
            </div>
        </div>
    );
};

export default SimpleCategoryForm;
