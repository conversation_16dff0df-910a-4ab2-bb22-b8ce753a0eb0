import React, { useState, useEffect } from 'react';

const SimpleCategoryForm = () => {
    // State for form data
    const [formData, setFormData] = useState({
        name: '',
        image: ''
    });
    
    // State for categories list
    const [categories, setCategories] = useState([]);
    
    // State for editing
    const [editingId, setEditingId] = useState(null);

    // API base URL
    const API_URL = 'http://localhost:3000/api';

    // Fetch categories when component loads
    useEffect(() => {
        fetchCategories();
    }, []);

    // Function to fetch all categories
    const fetchCategories = async () => {
        try {
            const response = await fetch(`${API_URL}/categories`);
            const data = await response.json();
            setCategories(data);
        } catch (error) {
            console.error('Error fetching categories:', error);
        }
    };

    // Handle input changes
    const handleInputChange = (e) => {
        const { name, value } = e.target;
        setFormData(prev => ({
            ...prev,
            [name]: value
        }));
    };

    // Handle form submission
    const handleSubmit = async (e) => {
        e.preventDefault();
        
        try {
            const url = editingId 
                ? `${API_URL}/categories/${editingId}`
                : `${API_URL}/categories`;
            
            const method = editingId ? 'PUT' : 'POST';

            const response = await fetch(url, {
                method: method,
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(formData),
            });

            if (response.ok) {
                // Reset form
                setFormData({ name: '', image: '' });
                setEditingId(null);
                
                // Refresh categories list
                fetchCategories();
                
                alert(editingId ? 'Category updated!' : 'Category created!');
            }
        } catch (error) {
            console.error('Error saving category:', error);
            console.log(error)
            alert('Error saving category');
        }
    };

    // Handle edit button click
    const handleEdit = (category) => {
        setFormData({
            name: category.name,
            image: category.image
        });
        setEditingId(category._id);
    };

    // Handle delete button click
    
    const handleDelete = async (id) => {
        if (window.confirm('Are you sure you want to delete this category?')) {
            try {
                const response = await fetch(`${API_URL}/categories/${id}`, {
                    method: 'DELETE',
                });

                if (response.ok) {
                    fetchCategories();
                    alert('Category deleted!');
                }
            } catch (error) {
                console.error('Error deleting category:', error);
                alert('Error deleting category');
            }
        }
    };

    // Handle cancel edit
    const handleCancel = () => {
        setFormData({ name: '', image: '' });
        setEditingId(null);
    };

    return (
        <div style={{
            padding: '20px',
            maxWidth: '1200px',
            margin: '0 auto',
            background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
            minHeight: '100vh'
        }}>
            <h2 style={{
                color: 'white',
                marginBottom: '40px',
                textAlign: 'center',
                fontSize: '2.5rem',
                fontWeight: '700',
                textShadow: '0 2px 4px rgba(0, 0, 0, 0.3)',
                letterSpacing: '-0.5px'
            }}>Category Management</h2>

            {/* Form Section */}
            <div style={{
                background: 'rgba(255, 255, 255, 0.95)',
                backdropFilter: 'blur(10px)',
                padding: '40px',
                borderRadius: '20px',
                boxShadow: '0 20px 40px rgba(0, 0, 0, 0.1)',
                marginBottom: '40px',
                border: '1px solid rgba(255, 255, 255, 0.2)'
            }}>
                <h3 style={{
                    color: '#2d3748',
                    marginBottom: '30px',
                    fontSize: '1.8rem',
                    fontWeight: '600',
                    textAlign: 'center'
                }}>{editingId ? 'Edit Category' : 'Add New Category'}</h3>

                <form onSubmit={handleSubmit}>
                    <div style={{ marginBottom: '25px' }}>
                        <label style={{
                            display: 'block',
                            marginBottom: '10px',
                            fontWeight: '600',
                            color: '#2d3748',
                            fontSize: '1rem',
                            letterSpacing: '0.3px'
                        }}>
                            Category Name:
                        </label>
                        <input
                            type="text"
                            name="name"
                            value={formData.name}
                            onChange={handleInputChange}
                            required
                            style={{
                                width: '100%',
                                padding: '15px 20px',
                                border: '2px solid #e2e8f0',
                                borderRadius: '12px',
                                fontSize: '1rem',
                                transition: 'all 0.3s ease',
                                boxSizing: 'border-box',
                                background: '#f8fafc',
                                fontFamily: 'inherit'
                            }}
                            placeholder="Enter category name (e.g., Clothing, Shoes)"
                            onFocus={(e) => {
                                e.target.style.borderColor = '#667eea';
                                e.target.style.boxShadow = '0 0 0 4px rgba(102, 126, 234, 0.1)';
                                e.target.style.background = 'white';
                                e.target.style.transform = 'translateY(-1px)';
                            }}
                            onBlur={(e) => {
                                e.target.style.borderColor = '#e2e8f0';
                                e.target.style.boxShadow = 'none';
                                e.target.style.background = '#f8fafc';
                                e.target.style.transform = 'translateY(0)';
                            }}
                        />
                    </div>

                    <div style={{ marginBottom: '25px' }}>
                        <label style={{
                            display: 'block',
                            marginBottom: '10px',
                            fontWeight: '600',
                            color: '#2d3748',
                            fontSize: '1rem',
                            letterSpacing: '0.3px'
                        }}>
                            Image URL:
                        </label>
                        <input
                            type="url"
                            name="image"
                            value={formData.image}
                            onChange={handleInputChange}
                            required
                            style={{
                                width: '100%',
                                padding: '15px 20px',
                                border: '2px solid #e2e8f0',
                                borderRadius: '12px',
                                fontSize: '1rem',
                                transition: 'all 0.3s ease',
                                boxSizing: 'border-box',
                                background: '#f8fafc',
                                fontFamily: 'inherit'
                            }}
                            placeholder="Enter image URL"
                            onFocus={(e) => {
                                e.target.style.borderColor = '#667eea';
                                e.target.style.boxShadow = '0 0 0 4px rgba(102, 126, 234, 0.1)';
                                e.target.style.background = 'white';
                                e.target.style.transform = 'translateY(-1px)';
                            }}
                            onBlur={(e) => {
                                e.target.style.borderColor = '#e2e8f0';
                                e.target.style.boxShadow = 'none';
                                e.target.style.background = '#f8fafc';
                                e.target.style.transform = 'translateY(0)';
                            }}
                        />
                    </div>

                    <div style={{
                        display: 'flex',
                        gap: '20px',
                        marginTop: '40px',
                        justifyContent: 'center'
                    }}>
                        <button
                            type="submit"
                            style={{
                                background: 'linear-gradient(135deg, #667eea, #764ba2)',
                                color: 'white',
                                border: 'none',
                                padding: '15px 35px',
                                borderRadius: '12px',
                                fontSize: '1.1rem',
                                fontWeight: '600',
                                cursor: 'pointer',
                                transition: 'all 0.3s ease',
                                boxShadow: '0 4px 15px rgba(102, 126, 234, 0.3)',
                                letterSpacing: '0.5px'
                            }}
                            onMouseEnter={(e) => {
                                e.target.style.background = 'linear-gradient(135deg, #5a67d8, #6b46c1)';
                                e.target.style.transform = 'translateY(-3px)';
                                e.target.style.boxShadow = '0 8px 25px rgba(102, 126, 234, 0.4)';
                            }}
                            onMouseLeave={(e) => {
                                e.target.style.background = 'linear-gradient(135deg, #667eea, #764ba2)';
                                e.target.style.transform = 'translateY(0)';
                                e.target.style.boxShadow = '0 4px 15px rgba(102, 126, 234, 0.3)';
                            }}
                        >
                            {editingId ? 'Update Category' : 'Add Category'}
                        </button>

                        {editingId && (
                            <button
                                type="button"
                                onClick={handleCancel}
                                style={{
                                    background: 'linear-gradient(135deg, #718096, #4a5568)',
                                    color: 'white',
                                    border: 'none',
                                    padding: '15px 35px',
                                    borderRadius: '12px',
                                    fontSize: '1.1rem',
                                    fontWeight: '600',
                                    cursor: 'pointer',
                                    transition: 'all 0.3s ease',
                                    boxShadow: '0 4px 15px rgba(113, 128, 150, 0.3)'
                                }}
                                onMouseEnter={(e) => {
                                    e.target.style.background = 'linear-gradient(135deg, #4a5568, #2d3748)';
                                    e.target.style.transform = 'translateY(-3px)';
                                    e.target.style.boxShadow = '0 8px 25px rgba(113, 128, 150, 0.4)';
                                }}
                                onMouseLeave={(e) => {
                                    e.target.style.background = 'linear-gradient(135deg, #718096, #4a5568)';
                                    e.target.style.transform = 'translateY(0)';
                                    e.target.style.boxShadow = '0 4px 15px rgba(113, 128, 150, 0.3)';
                                }}
                            >
                                Cancel
                            </button>
                        )}
                    </div>
                </form>
            </div>

            {/* Categories List Section */}
            <div>
                <h3>Existing Categories</h3>
                {categories.length === 0 ? (
                    <p>No categories found. Add your first category above.</p>
                ) : (
                    <div style={{ 
                        display: 'grid', 
                        gridTemplateColumns: 'repeat(auto-fill, minmax(250px, 1fr))', 
                        gap: '20px' 
                    }}>
                        {categories.map(category => (
                            <div 
                                key={category._id} 
                                style={{
                                    border: '1px solid #ddd',
                                    borderRadius: '8px',
                                    padding: '15px',
                                    backgroundColor: 'white'
                                }}
                            >
                                <img 
                                    src={category.image} 
                                    alt={category.name}
                                    style={{
                                        width: '100%',
                                        height: '150px',
                                        objectFit: 'cover',
                                        borderRadius: '4px',
                                        marginBottom: '10px'
                                    }}
                                />
                                <h4 style={{ margin: '0 0 10px 0' }}>{category.name}</h4>
                                <div>
                                    <button 
                                        onClick={() => handleEdit(category)}
                                        style={{
                                            backgroundColor: '#ffc107',
                                            color: 'black',
                                            padding: '5px 10px',
                                            border: 'none',
                                            borderRadius: '4px',
                                            cursor: 'pointer',
                                            marginRight: '5px'
                                        }}
                                    >
                                        Edit
                                    </button>
                                    <button 
                                        onClick={() => handleDelete(category._id)}
                                        style={{
                                            backgroundColor: '#dc3545',
                                            color: 'white',
                                            padding: '5px 10px',
                                            border: 'none',
                                            borderRadius: '4px',
                                            cursor: 'pointer'
                                        }}
                                    >
                                        Delete
                                    </button>
                                </div>
                            </div>
                        ))}
                    </div>
                )}
            </div>
        </div>
    );
};

export default SimpleCategoryForm;
