import React from 'react';

const SimpleNavigation = ({ activeTab, setActiveTab }) => {
    const tabs = [
        { id: 'categories', label: 'Categories' },
        { id: 'subcategories', label: 'Subcategories' },
        { id: 'products', label: 'Products' }
    ];

    return (
        <nav style={{
            backgroundColor: '#343a40',
            padding: '15px 0',
            marginBottom: '20px'
        }}>
            <div style={{
                maxWidth: '1000px',
                margin: '0 auto',
                display: 'flex',
                justifyContent: 'space-between',
                alignItems: 'center',
                padding: '0 20px'
            }}>
                <h1 style={{
                    color: 'white',
                    margin: 0,
                    fontSize: '24px'
                }}>
                    🛍️ Product Management System
                </h1>
                
                <div style={{ display: 'flex', gap: '10px' }}>
                    {tabs.map(tab => (
                        <button
                            key={tab.id}
                            onClick={() => setActiveTab(tab.id)}
                            style={{
                                backgroundColor: activeTab === tab.id ? '#007bff' : 'transparent',
                                color: 'white',
                                border: '2px solid #007bff',
                                padding: '8px 16px',
                                borderRadius: '4px',
                                cursor: 'pointer',
                                fontSize: '14px',
                                fontWeight: '600'
                            }}
                        >
                            {tab.label}
                        </button>
                    ))}
                </div>
            </div>
        </nav>
    );
};

export default SimpleNavigation;
