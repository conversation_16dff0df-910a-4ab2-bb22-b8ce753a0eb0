import React, { useState, useEffect } from 'react';

const SimpleProductForm = () => {
    // State for form data
    const [formData, setFormData] = useState({
        name: '',
        image: '',
        price: '',
        color: '',
        size: '',
        description: '',
        category: '',
        subCategory: ''
    });
    
    // State for lists
    const [categories, setCategories] = useState([]);
    const [subcategories, setSubcategories] = useState([]);
    const [products, setProducts] = useState([]);
    
    // State for editing
    const [editingId, setEditingId] = useState(null);

    // API base URL
    const API_URL = 'http://localhost:3000/api';

    // Fetch data when component loads
    useEffect(() => {
        fetchCategories();
        fetchProducts();
    }, []);

    // Fetch subcategories when category changes
    useEffect(() => {
        if (formData.category) {
            fetchSubcategoriesByCategory(formData.category);
        } else {
            setSubcategories([]);
            setFormData(prev => ({ ...prev, subCategory: '' }));
        }
    }, [formData.category]);

    // Function to fetch all categories
    const fetchCategories = async () => {
        try {
            const response = await fetch(`${API_URL}/categories`);
            const data = await response.json();
            setCategories(data);
        } catch (error) {
            console.error('Error fetching categories:', error);
        }
    };

    // Function to fetch subcategories by category
    const fetchSubcategoriesByCategory = async (categoryId) => {
        try {
            const response = await fetch(`${API_URL}/categories/${categoryId}/subcategories`);
            const data = await response.json();
            setSubcategories(data);
        } catch (error) {
            console.error('Error fetching subcategories:', error);
        }
    };

    // Function to fetch all products
    const fetchProducts = async () => {
        try {
            const response = await fetch(`${API_URL}/products`);
            const data = await response.json();
            setProducts(data);
        } catch (error) {
            console.error('Error fetching products:', error);
        }
    };

    // Handle input changes
    const handleInputChange = (e) => {
        const { name, value } = e.target;
        setFormData(prev => ({
            ...prev,
            [name]: value
        }));
    };

    // Handle form submission
    const handleSubmit = async (e) => {
        e.preventDefault();
        
        try {
            // Remove category from form data (not needed for API)
            const { category, ...productData } = formData;
            
            const url = editingId 
                ? `${API_URL}/products/${editingId}`
                : `${API_URL}/products`;
            
            const method = editingId ? 'PUT' : 'POST';

            const response = await fetch(url, {
                method: method,
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(productData),
            });

            if (response.ok) {
                // Reset form
                setFormData({ 
                    name: '', image: '', price: '', color: '', size: '', 
                    description: '', category: '', subCategory: '' 
                });
                setEditingId(null);
                
                // Refresh products list
                fetchProducts();
                
                alert(editingId ? 'Product updated!' : 'Product created!');
            }
        } catch (error) {
            console.error('Error saving product:', error);
            alert('Error saving product');
        }
    };

    // Handle edit button click
    const handleEdit = async (product) => {
        // First fetch subcategories for the product's category
        await fetchSubcategoriesByCategory(product.subCategory.category._id);
        
        setFormData({
            name: product.name,
            image: product.image,
            price: product.price.toString(),
            color: product.color,
            size: product.size,
            description: product.description,
            category: product.subCategory.category._id,
            subCategory: product.subCategory._id
        });
        setEditingId(product._id);
    };

    // Handle delete button click
    const handleDelete = async (id) => {
        if (window.confirm('Are you sure you want to delete this product?')) {
            try {
                const response = await fetch(`${API_URL}/products/${id}`, {
                    method: 'DELETE',
                });

                if (response.ok) {
                    fetchProducts();
                    alert('Product deleted!');
                }
            } catch (error) {
                console.error('Error deleting product:', error);
                alert('Error deleting product');
            }
        }
    };

    // Handle cancel edit
    const handleCancel = () => {
        setFormData({ 
            name: '', image: '', price: '', color: '', size: '', 
            description: '', category: '', subCategory: '' 
        });
        setEditingId(null);
    };

    return (
        <div style={{ padding: '20px', maxWidth: '1000px', margin: '0 auto' }}>
            <h2>Product Management</h2>
            
            {/* Form Section */}
            <div style={{ 
                backgroundColor: '#f5f5f5', 
                padding: '20px', 
                borderRadius: '8px', 
                marginBottom: '30px' 
            }}>
                <h3>{editingId ? 'Edit Product' : 'Add New Product'}</h3>
                
                <form onSubmit={handleSubmit}>
                    {/* Category and Subcategory Row */}
                    <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '15px', marginBottom: '15px' }}>
                        <div>
                            <label style={{ display: 'block', marginBottom: '5px' }}>
                                Select Category:
                            </label>
                            <select
                                name="category"
                                value={formData.category}
                                onChange={handleInputChange}
                                required
                                style={{
                                    width: '100%',
                                    padding: '8px',
                                    border: '1px solid #ddd',
                                    borderRadius: '4px'
                                }}
                            >
                                <option value="">Choose a category</option>
                                {categories.map(category => (
                                    <option key={category._id} value={category._id}>
                                        {category.name}
                                    </option>
                                ))}
                            </select>
                        </div>

                        <div>
                            <label style={{ display: 'block', marginBottom: '5px' }}>
                                Select Subcategory:
                            </label>
                            <select
                                name="subCategory"
                                value={formData.subCategory}
                                onChange={handleInputChange}
                                required
                                disabled={!formData.category}
                                style={{
                                    width: '100%',
                                    padding: '8px',
                                    border: '1px solid #ddd',
                                    borderRadius: '4px',
                                    backgroundColor: !formData.category ? '#f8f9fa' : 'white'
                                }}
                            >
                                <option value="">Choose a subcategory</option>
                                {subcategories.map(subcategory => (
                                    <option key={subcategory._id} value={subcategory._id}>
                                        {subcategory.name}
                                    </option>
                                ))}
                            </select>
                        </div>
                    </div>

                    {/* Product Name and Price Row */}
                    <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '15px', marginBottom: '15px' }}>
                        <div>
                            <label style={{ display: 'block', marginBottom: '5px' }}>
                                Product Name:
                            </label>
                            <input
                                type="text"
                                name="name"
                                value={formData.name}
                                onChange={handleInputChange}
                                required
                                style={{
                                    width: '100%',
                                    padding: '8px',
                                    border: '1px solid #ddd',
                                    borderRadius: '4px'
                                }}
                                placeholder="Enter product name"
                            />
                        </div>

                        <div>
                            <label style={{ display: 'block', marginBottom: '5px' }}>
                                Price:
                            </label>
                            <input
                                type="number"
                                name="price"
                                value={formData.price}
                                onChange={handleInputChange}
                                required
                                min="0"
                                step="0.01"
                                style={{
                                    width: '100%',
                                    padding: '8px',
                                    border: '1px solid #ddd',
                                    borderRadius: '4px'
                                }}
                                placeholder="Enter price"
                            />
                        </div>
                    </div>

                    {/* Color and Size Row */}
                    <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '15px', marginBottom: '15px' }}>
                        <div>
                            <label style={{ display: 'block', marginBottom: '5px' }}>
                                Color:
                            </label>
                            <input
                                type="text"
                                name="color"
                                value={formData.color}
                                onChange={handleInputChange}
                                required
                                style={{
                                    width: '100%',
                                    padding: '8px',
                                    border: '1px solid #ddd',
                                    borderRadius: '4px'
                                }}
                                placeholder="Enter color"
                            />
                        </div>

                        <div>
                            <label style={{ display: 'block', marginBottom: '5px' }}>
                                Size:
                            </label>
                            <input
                                type="text"
                                name="size"
                                value={formData.size}
                                onChange={handleInputChange}
                                required
                                style={{
                                    width: '100%',
                                    padding: '8px',
                                    border: '1px solid #ddd',
                                    borderRadius: '4px'
                                }}
                                placeholder="Enter size"
                            />
                        </div>
                    </div>

                    {/* Image URL */}
                    <div style={{ marginBottom: '15px' }}>
                        <label style={{ display: 'block', marginBottom: '5px' }}>
                            Image URL:
                        </label>
                        <input
                            type="url"
                            name="image"
                            value={formData.image}
                            onChange={handleInputChange}
                            required
                            style={{
                                width: '100%',
                                padding: '8px',
                                border: '1px solid #ddd',
                                borderRadius: '4px'
                            }}
                            placeholder="Enter image URL"
                        />
                    </div>

                    {/* Description */}
                    <div style={{ marginBottom: '15px' }}>
                        <label style={{ display: 'block', marginBottom: '5px' }}>
                            Description:
                        </label>
                        <textarea
                            name="description"
                            value={formData.description}
                            onChange={handleInputChange}
                            required
                            rows="3"
                            style={{
                                width: '100%',
                                padding: '8px',
                                border: '1px solid #ddd',
                                borderRadius: '4px',
                                resize: 'vertical'
                            }}
                            placeholder="Enter product description"
                        />
                    </div>

                    <div>
                        <button 
                            type="submit"
                            style={{
                                backgroundColor: '#17a2b8',
                                color: 'white',
                                padding: '10px 20px',
                                border: 'none',
                                borderRadius: '4px',
                                cursor: 'pointer',
                                marginRight: '10px'
                            }}
                        >
                            {editingId ? 'Update Product' : 'Add Product'}
                        </button>
                        
                        {editingId && (
                            <button 
                                type="button"
                                onClick={handleCancel}
                                style={{
                                    backgroundColor: '#6c757d',
                                    color: 'white',
                                    padding: '10px 20px',
                                    border: 'none',
                                    borderRadius: '4px',
                                    cursor: 'pointer'
                                }}
                            >
                                Cancel
                            </button>
                        )}
                    </div>
                </form>
            </div>

            {/* Products List Section */}
            <div>
                <h3>Existing Products</h3>
                {products.length === 0 ? (
                    <p>No products found. Add your first product above.</p>
                ) : (
                    <div style={{ 
                        display: 'grid', 
                        gridTemplateColumns: 'repeat(auto-fill, minmax(300px, 1fr))', 
                        gap: '20px' 
                    }}>
                        {products.map(product => (
                            <div 
                                key={product._id} 
                                style={{
                                    border: '1px solid #ddd',
                                    borderRadius: '8px',
                                    padding: '15px',
                                    backgroundColor: 'white'
                                }}
                            >
                                <img 
                                    src={product.image} 
                                    alt={product.name}
                                    style={{
                                        width: '100%',
                                        height: '200px',
                                        objectFit: 'cover',
                                        borderRadius: '4px',
                                        marginBottom: '10px'
                                    }}
                                />
                                <h4 style={{ margin: '0 0 5px 0' }}>{product.name}</h4>
                                <p style={{ margin: '0 0 5px 0', fontWeight: 'bold', color: '#28a745' }}>
                                    ${product.price}
                                </p>
                                <p style={{ margin: '0 0 5px 0', fontSize: '14px' }}>
                                    Color: {product.color} | Size: {product.size}
                                </p>
                                <p style={{ margin: '0 0 10px 0', fontSize: '14px', color: '#666' }}>
                                    {product.description}
                                </p>
                                <div style={{ marginBottom: '10px' }}>
                                    <span style={{ 
                                        backgroundColor: '#007bff', 
                                        color: 'white', 
                                        padding: '2px 8px', 
                                        borderRadius: '12px', 
                                        fontSize: '12px',
                                        marginRight: '5px'
                                    }}>
                                        {product.subCategory.category.name}
                                    </span>
                                    <span style={{ 
                                        backgroundColor: '#28a745', 
                                        color: 'white', 
                                        padding: '2px 8px', 
                                        borderRadius: '12px', 
                                        fontSize: '12px'
                                    }}>
                                        {product.subCategory.name}
                                    </span>
                                </div>
                                <div>
                                    <button 
                                        onClick={() => handleEdit(product)}
                                        style={{
                                            backgroundColor: '#ffc107',
                                            color: 'black',
                                            padding: '5px 10px',
                                            border: 'none',
                                            borderRadius: '4px',
                                            cursor: 'pointer',
                                            marginRight: '5px'
                                        }}
                                    >
                                        Edit
                                    </button>
                                    <button 
                                        onClick={() => handleDelete(product._id)}
                                        style={{
                                            backgroundColor: '#dc3545',
                                            color: 'white',
                                            padding: '5px 10px',
                                            border: 'none',
                                            borderRadius: '4px',
                                            cursor: 'pointer'
                                        }}
                                    >
                                        Delete
                                    </button>
                                </div>
                            </div>
                        ))}
                    </div>
                )}
            </div>
        </div>
    );
};

export default SimpleProductForm;
