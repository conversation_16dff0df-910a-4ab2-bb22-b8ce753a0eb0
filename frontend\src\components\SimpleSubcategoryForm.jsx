import React, { useState, useEffect } from 'react';

const SimpleSubcategoryForm = () => {
    // State for form data
    const [formData, setFormData] = useState({
        name: '',
        image: '',
        category: ''
    });
    
    // State for lists
    const [categories, setCategories] = useState([]);
    const [subcategories, setSubcategories] = useState([]);
    
    // State for editing
    const [editingId, setEditingId] = useState(null);

    // API base URL
    const API_URL = 'http://localhost:3000/api';

    // Fetch data when component loads
    useEffect(() => {
        fetchCategories();
        fetchSubcategories();
    }, []);

    // Function to fetch all categories
    const fetchCategories = async () => {
        try {
            const response = await fetch(`${API_URL}/categories`);
            const data = await response.json();
            setCategories(data);
        } catch (error) {
            console.error('Error fetching categories:', error);
        }
    };

    // Function to fetch all subcategories
    const fetchSubcategories = async () => {
        try {
            const response = await fetch(`${API_URL}/subcategories`);
            const data = await response.json();
            setSubcategories(data);
        } catch (error) {
            console.error('Error fetching subcategories:', error);
        }
    };

    // Handle input changes
    const handleInputChange = (e) => {
        const { name, value } = e.target;
        setFormData(prev => ({
            ...prev,
            [name]: value
        }));
    };

    // Handle form submission
    const handleSubmit = async (e) => {
        e.preventDefault();
        
        try {
            const url = editingId 
                ? `${API_URL}/subcategories/${editingId}`
                : `${API_URL}/subcategories`;
            
            const method = editingId ? 'PUT' : 'POST';

            const response = await fetch(url, {
                method: method,
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(formData),
            });

            if (response.ok) {
                // Reset form
                setFormData({ name: '', image: '', category: '' });
                setEditingId(null);
                
                // Refresh subcategories list
                fetchSubcategories();
                
                alert(editingId ? 'Subcategory updated!' : 'Subcategory created!');
            }
        } catch (error) {
            console.error('Error saving subcategory:', error);
            alert('Error saving subcategory');
        }
    };

    // Handle edit button click
    const handleEdit = (subcategory) => {
        setFormData({
            name: subcategory.name,
            image: subcategory.image,
            category: subcategory.category._id
        });
        setEditingId(subcategory._id);
    };

    // Handle delete button click
    const handleDelete = async (id) => {
        if (window.confirm('Are you sure you want to delete this subcategory?')) {
            try {
                const response = await fetch(`${API_URL}/subcategories/${id}`, {
                    method: 'DELETE',
                });

                if (response.ok) {
                    fetchSubcategories();
                    alert('Subcategory deleted!');
                }
            } catch (error) {
                console.error('Error deleting subcategory:', error);
                alert('Error deleting subcategory');
            }
        }
    };

    // Handle cancel edit
    const handleCancel = () => {
        setFormData({ name: '', image: '', category: '' });
        setEditingId(null);
    };

    return (
        <div style={{ padding: '20px', maxWidth: '800px', margin: '0 auto' }}>
            <h2>Subcategory Management</h2>
            
            {/* Form Section */}
            <div style={{ 
                backgroundColor: '#f5f5f5', 
                padding: '20px', 
                borderRadius: '8px', 
                marginBottom: '30px' 
            }}>
                <h3>{editingId ? 'Edit Subcategory' : 'Add New Subcategory'}</h3>
                
                <form onSubmit={handleSubmit}>
                    {/* Category Dropdown */}
                    <div style={{ marginBottom: '15px' }}>
                        <label style={{ display: 'block', marginBottom: '5px' }}>
                            Select Category:
                        </label>
                        <select
                            name="category"
                            value={formData.category}
                            onChange={handleInputChange}
                            required
                            style={{
                                width: '100%',
                                padding: '8px',
                                border: '1px solid #ddd',
                                borderRadius: '4px'
                            }}
                        >
                            <option value="">Choose a category</option>
                            {categories.map(category => (
                                <option key={category._id} value={category._id}>
                                    {category.name}
                                </option>
                            ))}
                        </select>
                    </div>

                    <div style={{ marginBottom: '15px' }}>
                        <label style={{ display: 'block', marginBottom: '5px' }}>
                            Subcategory Name:
                        </label>
                        <input
                            type="text"
                            name="name"
                            value={formData.name}
                            onChange={handleInputChange}
                            required
                            style={{
                                width: '100%',
                                padding: '8px',
                                border: '1px solid #ddd',
                                borderRadius: '4px'
                            }}
                            placeholder="Enter subcategory name (e.g., Shirts, Formal Shoes)"
                        />
                    </div>

                    <div style={{ marginBottom: '15px' }}>
                        <label style={{ display: 'block', marginBottom: '5px' }}>
                            Image URL:
                        </label>
                        <input
                            type="url"
                            name="image"
                            value={formData.image}
                            onChange={handleInputChange}
                            required
                            style={{
                                width: '100%',
                                padding: '8px',
                                border: '1px solid #ddd',
                                borderRadius: '4px'
                            }}
                            placeholder="Enter image URL"
                        />
                    </div>

                    <div>
                        <button 
                            type="submit"
                            style={{
                                backgroundColor: '#28a745',
                                color: 'white',
                                padding: '10px 20px',
                                border: 'none',
                                borderRadius: '4px',
                                cursor: 'pointer',
                                marginRight: '10px'
                            }}
                        >
                            {editingId ? 'Update Subcategory' : 'Add Subcategory'}
                        </button>
                        
                        {editingId && (
                            <button 
                                type="button"
                                onClick={handleCancel}
                                style={{
                                    backgroundColor: '#6c757d',
                                    color: 'white',
                                    padding: '10px 20px',
                                    border: 'none',
                                    borderRadius: '4px',
                                    cursor: 'pointer'
                                }}
                            >
                                Cancel
                            </button>
                        )}
                    </div>
                </form>
            </div>

            {/* Subcategories List Section */}
            <div>
                <h3>Existing Subcategories</h3>
                {subcategories.length === 0 ? (
                    <p>No subcategories found. Add your first subcategory above.</p>
                ) : (
                    <div style={{ 
                        display: 'grid', 
                        gridTemplateColumns: 'repeat(auto-fill, minmax(250px, 1fr))', 
                        gap: '20px' 
                    }}>
                        {subcategories.map(subcategory => (
                            <div 
                                key={subcategory._id} 
                                style={{
                                    border: '1px solid #ddd',
                                    borderRadius: '8px',
                                    padding: '15px',
                                    backgroundColor: 'white'
                                }}
                            >
                                <img 
                                    src={subcategory.image} 
                                    alt={subcategory.name}
                                    style={{
                                        width: '100%',
                                        height: '150px',
                                        objectFit: 'cover',
                                        borderRadius: '4px',
                                        marginBottom: '10px'
                                    }}
                                />
                                <h4 style={{ margin: '0 0 5px 0' }}>{subcategory.name}</h4>
                                <p style={{ 
                                    margin: '0 0 10px 0', 
                                    color: '#666',
                                    fontSize: '14px'
                                }}>
                                    Category: {subcategory.category.name}
                                </p>
                                <div>
                                    <button 
                                        onClick={() => handleEdit(subcategory)}
                                        style={{
                                            backgroundColor: '#ffc107',
                                            color: 'black',
                                            padding: '5px 10px',
                                            border: 'none',
                                            borderRadius: '4px',
                                            cursor: 'pointer',
                                            marginRight: '5px'
                                        }}
                                    >
                                        Edit
                                    </button>
                                    <button 
                                        onClick={() => handleDelete(subcategory._id)}
                                        style={{
                                            backgroundColor: '#dc3545',
                                            color: 'white',
                                            padding: '5px 10px',
                                            border: 'none',
                                            borderRadius: '4px',
                                            cursor: 'pointer'
                                        }}
                                    >
                                        Delete
                                    </button>
                                </div>
                            </div>
                        ))}
                    </div>
                )}
            </div>
        </div>
    );
};

export default SimpleSubcategoryForm;
