import React, { useState, useEffect } from 'react';
import './FormStyles.css';

const SubcategoryForm = ({ onSubcategoryAdded }) => {
    const [categories, setCategories] = useState([]);
    const [subcategories, setSubcategories] = useState([]);
    const [formData, setFormData] = useState({
        name: '',
        image: '',
        category: ''
    });
    const [editingId, setEditingId] = useState(null);
    const [loading, setLoading] = useState(false);
    const [selectedCategoryFilter, setSelectedCategoryFilter] = useState('');

    const API_BASE_URL = 'http://localhost:3000/api';

    // Fetch data on component mount
    useEffect(() => {
        fetchCategories();
        fetchSubcategories();
    }, []);

    const fetchCategories = async () => {
        try {
            const response = await fetch(`${API_BASE_URL}/categories`);
            const data = await response.json();
            setCategories(data);
        } catch (error) {
            console.error('Error fetching categories:', error);
        }
    };

    const fetchSubcategories = async () => {
        try {
            const response = await fetch(`${API_BASE_URL}/subcategories`);
            const data = await response.json();
            setSubcategories(data);
        } catch (error) {
            console.error('Error fetching subcategories:', error);
        }
    };

    const handleInputChange = (e) => {
        const { name, value } = e.target;
        setFormData(prev => ({
            ...prev,
            [name]: value
        }));
    };

    const handleSubmit = async (e) => {
        e.preventDefault();
        setLoading(true);

        try {
            const url = editingId 
                ? `${API_BASE_URL}/subcategories/${editingId}`
                : `${API_BASE_URL}/subcategories`;
            
            const method = editingId ? 'PUT' : 'POST';

            const response = await fetch(url, {
                method,
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(formData),
            });

            if (response.ok) {
                const savedSubcategory = await response.json();
                
                if (editingId) {
                    setSubcategories(prev => 
                        prev.map(sub => sub._id === editingId ? savedSubcategory : sub)
                    );
                } else {
                    setSubcategories(prev => [...prev, savedSubcategory]);
                }

                // Reset form
                setFormData({ name: '', image: '', category: '' });
                setEditingId(null);
                
                // Notify parent component
                if (onSubcategoryAdded) {
                    onSubcategoryAdded(savedSubcategory);
                }
            } else {
                const errorData = await response.json();
                alert(`Error: ${errorData.message}`);
            }
        } catch (error) {
            console.error('Error saving subcategory:', error);
            alert('Error saving subcategory');
        } finally {
            setLoading(false);
        }
    };

    const handleEdit = (subcategory) => {
        setFormData({
            name: subcategory.name,
            image: subcategory.image,
            category: subcategory.category._id
        });
        setEditingId(subcategory._id);
    };

    const handleDelete = async (id) => {
        if (window.confirm('Are you sure you want to delete this subcategory? This will also delete all related products.')) {
            try {
                const response = await fetch(`${API_BASE_URL}/subcategories/${id}`, {
                    method: 'DELETE',
                });

                if (response.ok) {
                    setSubcategories(prev => prev.filter(sub => sub._id !== id));
                } else {
                    const errorData = await response.json();
                    alert(`Error: ${errorData.message}`);
                }
            } catch (error) {
                console.error('Error deleting subcategory:', error);
                alert('Error deleting subcategory');
            }
        }
    };

    const handleCancel = () => {
        setFormData({ name: '', image: '', category: '' });
        setEditingId(null);
    };

    // Filter subcategories based on selected category
    const filteredSubcategories = selectedCategoryFilter 
        ? subcategories.filter(sub => sub.category._id === selectedCategoryFilter)
        : subcategories;

    return (
        <div className="form-container">
            <h2>Subcategory Management</h2>
            
            <form onSubmit={handleSubmit} className="form">
                <div className="form-group">
                    <label htmlFor="category">Select Category:</label>
                    <select
                        id="category"
                        name="category"
                        value={formData.category}
                        onChange={handleInputChange}
                        required
                    >
                        <option value="">Choose a category</option>
                        {categories.map(category => (
                            <option key={category._id} value={category._id}>
                                {category.name}
                            </option>
                        ))}
                    </select>
                </div>

                <div className="form-group">
                    <label htmlFor="name">Subcategory Name:</label>
                    <input
                        type="text"
                        id="name"
                        name="name"
                        value={formData.name}
                        onChange={handleInputChange}
                        required
                        placeholder="Enter subcategory name"
                    />
                </div>

                <div className="form-group">
                    <label htmlFor="image">Image URL:</label>
                    <input
                        type="url"
                        id="image"
                        name="image"
                        value={formData.image}
                        onChange={handleInputChange}
                        required
                        placeholder="Enter image URL"
                    />
                </div>

                <div className="form-actions">
                    <button type="submit" disabled={loading} className="btn-primary">
                        {loading ? 'Saving...' : editingId ? 'Update Subcategory' : 'Add Subcategory'}
                    </button>
                    {editingId && (
                        <button type="button" onClick={handleCancel} className="btn-secondary">
                            Cancel
                        </button>
                    )}
                </div>
            </form>

            <div className="items-list">
                <div className="list-header">
                    <h3>Existing Subcategories</h3>
                    <div className="filter-group">
                        <label htmlFor="categoryFilter">Filter by Category:</label>
                        <select
                            id="categoryFilter"
                            value={selectedCategoryFilter}
                            onChange={(e) => setSelectedCategoryFilter(e.target.value)}
                        >
                            <option value="">All Categories</option>
                            {categories.map(category => (
                                <option key={category._id} value={category._id}>
                                    {category.name}
                                </option>
                            ))}
                        </select>
                    </div>
                </div>

                {filteredSubcategories.length === 0 ? (
                    <p>No subcategories found. Add your first subcategory above.</p>
                ) : (
                    <div className="items-grid">
                        {filteredSubcategories.map(subcategory => (
                            <div key={subcategory._id} className="item-card">
                                <img src={subcategory.image} alt={subcategory.name} className="item-image" />
                                <div className="item-content">
                                    <h4>{subcategory.name}</h4>
                                    <p className="category-badge">{subcategory.category.name}</p>
                                    <div className="item-actions">
                                        <button 
                                            onClick={() => handleEdit(subcategory)}
                                            className="btn-edit"
                                        >
                                            Edit
                                        </button>
                                        <button 
                                            onClick={() => handleDelete(subcategory._id)}
                                            className="btn-delete"
                                        >
                                            Delete
                                        </button>
                                    </div>
                                </div>
                            </div>
                        ))}
                    </div>
                )}
            </div>
        </div>
    );
};

export default SubcategoryForm;
